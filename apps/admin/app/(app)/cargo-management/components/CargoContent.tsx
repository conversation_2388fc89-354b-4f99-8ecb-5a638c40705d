"use client";

import { memo, useMemo } from "react";
import { Listing } from "@/modules/listing";
import { Button } from "@workspace/ui/components/button";
import { Trash2, FileText, Plus, Download, CheckSquare } from "lucide-react";
import { ExportDialog } from "@/components/ui/export-dialog";
import { ProtectedCreateButton } from "@/lib/components/RBACWrapper";
import {
  BulkStatusSelector,
  CARGO_STATUS_OPTIONS,
} from "@/components/ui/bulk-status-selector";
import {
  type CargoManagementState,
  type CargoDisplay,
  ITEMS_PER_PAGE,
} from "../utils/types";
import {
  type ColumnFilter,
  type TableColumn,
} from "@/components/ui/filter-panel";
import {
  CargoTableColumns,
  CargoCardRenderer,
  NewCargoDialog,
  QRCodeDialog,
} from "./index";

interface CargoContentProps {
  state: CargoManagementState;
  onStateUpdate: (updates: Partial<CargoManagementState>) => void;
  onRefresh: () => void;
  onCargoAction: (action: string, cargo?: CargoDisplay) => void;
  onCargoMutation: () => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onBulkCreateInvoice: () => void;
  onBulkCreateTasks: () => void;
  onClearSelections: () => void;
  onExportSelected: () => void;
  onExportAll: () => void;
  onCloseExportDialog: () => void;
}

/**
 * Content component for Cargo Management
 *
 * Handles the main listing, filtering, and display of cargo data.
 * Uses the Listing layout component for consistent UI patterns.
 */
export const CargoContent = memo<CargoContentProps>(
  ({
    state,
    onStateUpdate,
    onRefresh,
    onCargoAction,
    onCargoMutation,
    onBulkStatusUpdate,
    onBulkDelete,
    onBulkCreateInvoice,
    onBulkCreateTasks,
    onClearSelections,
    onExportSelected,
    onExportAll,
    onCloseExportDialog,
  }) => {
    // Render bulk actions
    const renderBulkActions = () => (
      <>
        <BulkStatusSelector
          statusOptions={CARGO_STATUS_OPTIONS}
          onStatusUpdate={onBulkStatusUpdate}
          disabled={state.bulkActionLoading}
          placeholder="Mark as..."
          onAfterUpdate={onRefresh}
        />
        <Button
          variant="outline"
          size="sm"
          onClick={onExportSelected}
          className="gap-2"
          disabled={state.selectedCargos.size === 0}
        >
          <Download size={16} />
          Export Selected
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onBulkCreateInvoice}
          className="gap-2"
          disabled={state.bulkActionLoading}
        >
          <FileText size={16} />
          Create Invoice
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onBulkCreateTasks}
          className="gap-2"
          disabled={state.bulkActionLoading || state.selectedCargos.size === 0}
        >
          <CheckSquare size={16} />
          Create Tasks
        </Button>
        <Button
          variant="destructive"
          size="sm"
          onClick={onBulkDelete}
          className="gap-2"
        >
          <Trash2 size={16} />
          Delete
        </Button>
      </>
    );

    // Define table columns for dynamic filtering
    const cargoTableColumns: TableColumn[] = [
      {
        key: "trackingNumber",
        label: "Tracking Number",
        type: "string",
        searchable: true,
      },
      {
        key: "chinaTrackingNumber",
        label: "China Tracking Number",
        type: "string",
        searchable: true,
      },
      {
        key: "batchCode",
        label: "Batch Code",
        type: "string",
        searchable: true,
      },
      { key: "type", label: "Type", type: "enum", searchable: true },
      { key: "origin", label: "Origin", type: "string", searchable: true },
      {
        key: "destination",
        label: "Destination",
        type: "string",
        searchable: true,
      },
      { key: "status", label: "Status", type: "enum", searchable: true },
      { key: "ctn", label: "CTN", type: "number", searchable: true },
      { key: "cbm", label: "CBM", type: "number", searchable: true },
      { key: "weight", label: "Weight", type: "string", searchable: true },
      { key: "customer", label: "Customer", type: "string", searchable: true },
      { key: "date", label: "Date", type: "date", searchable: true },
      {
        key: "particular",
        label: "Description",
        type: "string",
        searchable: true,
      },
    ];

    // Helper function to check if a value matches a filter
    const matchesFilter = (value: any, filterValue: string): boolean => {
      if (value == null) return false;

      // Handle different data types
      if (Array.isArray(value)) {
        return value.some((item: any) =>
          String(item).toLowerCase().includes(filterValue.toLowerCase())
        );
      } else if (typeof value === "string") {
        return value.toLowerCase().includes(filterValue.toLowerCase());
      } else if (typeof value === "number") {
        return String(value) === filterValue;
      } else if (value instanceof Date) {
        const dateStr = value.toISOString().split("T")[0];
        return dateStr === filterValue;
      } else {
        return String(value).toLowerCase().includes(filterValue.toLowerCase());
      }
    };

    // Filter and paginate data
    const filteredData = useMemo(() => {
      let filtered = state.cargoData;

      // Apply search term filter (client-side for better pagination)
      if (state.searchTerm.trim()) {
        const searchLower = state.searchTerm.toLowerCase();
        filtered = filtered.filter((cargo) => {
          return (
            cargo.trackingNumber.toLowerCase().includes(searchLower) ||
            cargo.chinaTrackingNumber?.toLowerCase().includes(searchLower) ||
            cargo.particular?.toLowerCase().includes(searchLower) ||
            cargo.customer.toLowerCase().includes(searchLower) ||
            cargo.type.toLowerCase().includes(searchLower) ||
            cargo.origin.toLowerCase().includes(searchLower) ||
            cargo.destination.toLowerCase().includes(searchLower) ||
            cargo.status.toLowerCase().includes(searchLower) ||
            cargo.batchCode?.toLowerCase().includes(searchLower)
          );
        });
      }

      // Apply category filter
      if (state.categoryFilter !== "all") {
        const statusMap: { [key: string]: string[] } = {
          "in-transit": ["IN_TRANSIT", "PROCESSING"],
          delivered: ["COMPLETED"],
          processing: ["PROCESSING", "CREATED"],
          issues: ["CANCELLED", "DELAYED", "FAILED"],
        };

        const statuses = statusMap[state.categoryFilter] || [];
        filtered = filtered.filter((cargo) =>
          statuses.some(
            (status) => cargo.status.toUpperCase().replace(" ", "_") === status
          )
        );
      }

      // Apply column filters (with safety check)
      if (
        Array.isArray(state.columnFilters) &&
        state.columnFilters.length > 0
      ) {
        filtered = filtered.filter((cargo) => {
          return state.columnFilters.every((filter: ColumnFilter) => {
            const cargoValue = cargo[filter.column as keyof CargoDisplay];
            return matchesFilter(cargoValue, filter.value);
          });
        });
      }

      return filtered;
    }, [
      state.cargoData,
      state.searchTerm,
      state.categoryFilter,
      state.columnFilters,
    ]);

    // Pagination (for cards view only - table handles its own pagination)
    const totalPages = Math.ceil(filteredData.length / ITEMS_PER_PAGE);
    const paginatedData = useMemo(() => {
      const start = (state.currentPage - 1) * ITEMS_PER_PAGE;
      const end = start + ITEMS_PER_PAGE;
      return filteredData.slice(start, end);
    }, [filteredData, state.currentPage]);

    // Categories for filtering
    const categories = [
      { key: "in-transit", label: "In Transit" },
      { key: "delivered", label: "Delivered" },
      { key: "processing", label: "Processing" },
      { key: "issues", label: "Issues" },
    ];

    return (
      <Listing className="space-y-6">
        <Listing.Filters
          searchTerm={state.searchTerm}
          onSearchChange={(term: string) =>
            onStateUpdate({ searchTerm: term, currentPage: 1 })
          }
          onRefresh={onRefresh}
          loading={state.loading}
          columnFilters={state.columnFilters}
          onColumnFilterAdd={(filter: ColumnFilter) =>
            onStateUpdate({
              columnFilters: [...state.columnFilters, filter],
              currentPage: 1,
            })
          }
          onColumnFilterRemove={(index: number) =>
            onStateUpdate({
              columnFilters: state.columnFilters.filter((_, i) => i !== index),
              currentPage: 1,
            })
          }
          enableDynamicFilters={true}
          columns={cargoTableColumns}
          tableData={state.cargoData}
          defaultFilterColumn="trackingNumber"
          autoSelectDefaultColumn={true}
          bulkActions={renderBulkActions()}
          selectedCount={state.selectedCargos.size}
          showBulkActions={true}
          onClearSelections={onClearSelections}
        />

        <Listing.Controls
          entity="cargo"
          length={filteredData.length}
          viewMode={state.viewMode}
          onViewModeChange={(mode: "cards" | "table") =>
            onStateUpdate({ viewMode: mode })
          }
          categoryFilter={state.categoryFilter}
          onCategoryFilterChange={(filter: string) =>
            onStateUpdate({ categoryFilter: filter, currentPage: 1 })
          }
          categories={categories}
          actions={
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={onExportAll} className="gap-2">
                <Download size={16} />
                Export All
              </Button>
              <ProtectedCreateButton entity="cargo">
                <Button onClick={() => onStateUpdate({ isNewCargoOpen: true })}>
                  <Plus size={16} />
                  New Cargo
                </Button>
              </ProtectedCreateButton>
            </div>
          }
        />

        {state.viewMode === "cards" ? (
          <Listing.Cards
            data={paginatedData}
            loading={state.loading}
            renderCard={(cargo: CargoDisplay) => (
              <CargoCardRenderer cargo={cargo} onAction={onCargoAction} />
            )}
            emptyState={
              <div className="text-center py-12">
                <p className="text-gray-500">No cargo found</p>
              </div>
            }
          />
        ) : (
          <Listing.Table
            data={filteredData}
            columns={CargoTableColumns({
              onAction: onCargoAction,
            })}
            loading={state.loading}
            enableCheckboxes={true}
            selectedRowIds={Array.from(state.selectedCargos)}
            onSelectionChange={(selectedIds) =>
              onStateUpdate({ selectedCargos: new Set(selectedIds) })
            }
            getRowId={(item) => item.id}
            pagination={{
              currentPage: state.currentPage,
              totalPages,
              totalItems: filteredData.length,
              itemsPerPage: ITEMS_PER_PAGE,
              onPageChange: (page: number) =>
                onStateUpdate({ currentPage: page }),
            }}
            emptyState={
              <div className="text-center py-12">
                <p className="text-gray-500">No cargo found</p>
              </div>
            }
          />
        )}

        {/* Dialogs */}
        <NewCargoDialog
          open={state.isNewCargoOpen}
          onOpenChange={(open) => onStateUpdate({ isNewCargoOpen: open })}
          onSuccess={onCargoMutation}
        />

        <QRCodeDialog
          open={state.qrCodeDialog.open}
          cargo={state.qrCodeDialog.cargo}
          onOpenChange={(open) =>
            onStateUpdate({
              qrCodeDialog: {
                open,
                cargo: open ? state.qrCodeDialog.cargo : null,
              },
            })
          }
        />

        {/* Export Dialog */}
        <ExportDialog
          isOpen={state.exportDialog.isOpen}
          onClose={onCloseExportDialog}
          data={state.exportDialog.data}
          title={state.exportDialog.config.title}
          filename={state.exportDialog.config.filename}
          excludeColumns={state.exportDialog.config.excludeColumns}
          columnMapping={state.exportDialog.config.columnMapping}
          onSuccess={(type, filename) => {
            console.log(`Export successful: ${type} - ${filename}`);
          }}
          onError={(error) => {
            console.error("Export error:", error);
          }}
        />
      </Listing>
    );
  }
);

CargoContent.displayName = "CargoContent";
