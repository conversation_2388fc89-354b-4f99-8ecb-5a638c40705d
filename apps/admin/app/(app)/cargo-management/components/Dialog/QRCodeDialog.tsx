"use client";

import { memo, useState, useEffect, useRef } from "react";
import QRCode from "qrcode";
import { QrC<PERSON>, Loader2, Printer, Share2, Copy } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@workspace/ui/components/dialog";
import { type CargoDisplay } from "../../utils/types";
import { toast } from "sonner";

interface QRCodeDialogProps {
  open: boolean;
  cargo: CargoDisplay | null;
  onOpenChange: (open: boolean) => void;
}

/**
 * QR Code Dialog Component
 *
 * Generates and displays QR codes for cargo tracking.
 * Includes print, share, and copy functionality.
 * Memoized to prevent unnecessary re-renders.
 */
export const QRCodeDialog = memo<QRCodeDialogProps>(
  ({ open, cargo, onOpenChange }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>("");
    const [isGenerating, setIsGenerating] = useState(false);

    // Generate QR code when dialog opens and cargo is available
    useEffect(() => {
      if (open && cargo) {
        generateQRCode();
      }
    }, [open, cargo]);

    const generateQRCode = async () => {
      if (!cargo) return;

      setIsGenerating(true);
      try {
        // Create URL for cargo tracking
        const cargoUrl = `${window.location.origin}/cargo-management/${cargo.id}`;

        // Generate QR code as data URL
        const qrCodeUrl = await QRCode.toDataURL(cargoUrl, {
          width: 256,
          margin: 2,
          color: {
            dark: "#000000",
            light: "#FFFFFF",
          },
        });

        setQrCodeDataUrl(qrCodeUrl);
      } catch (error) {
        console.error("Error generating QR code:", error);
        toast.error("Failed to generate QR code");
      } finally {
        setIsGenerating(false);
      }
    };

    const handlePrint = () => {
      if (!qrCodeDataUrl || !cargo) return;

      const printWindow = window.open("", "_blank");
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>QR Code - ${cargo.trackingNumber}</title>
              <style>
                body { 
                  font-family: Arial, sans-serif; 
                  text-align: center; 
                  padding: 20px; 
                }
                .qr-container { 
                  margin: 20px auto; 
                  max-width: 300px; 
                }
                .cargo-info { 
                  margin: 20px 0; 
                  font-size: 14px; 
                }
                .cargo-info h3 { 
                  margin: 10px 0; 
                  color: #333; 
                }
                .cargo-info p { 
                  margin: 5px 0; 
                  color: #666; 
                }
                img { 
                  max-width: 100%; 
                  height: auto; 
                }
              </style>
            </head>
            <body>
              <h2>Cargo QR Code</h2>
              <div class="cargo-info">
                <h3>${cargo.trackingNumber}</h3>
                <p><strong>Type:</strong> ${cargo.type}</p>
                <p><strong>Route:</strong> ${cargo.origin} → ${cargo.destination}</p>
                <p><strong>Status:</strong> ${cargo.status}</p>
                <p><strong>Weight:</strong> ${cargo.weight}</p>
                <p><strong>Customer:</strong> ${cargo.customer}</p>
                <p><strong>Date:</strong> ${cargo.date}</p>
              </div>
              <div class="qr-container">
                <img src="${qrCodeDataUrl}" alt="QR Code for ${cargo.trackingNumber}" />
              </div>
              <p style="font-size: 12px; color: #888; margin-top: 20px;">
                Scan this QR code to track cargo: ${cargo.trackingNumber}
              </p>
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    };

    const handleShare = async () => {
      if (!cargo) return;

      const cargoUrl = `${window.location.origin}/cargo-management/${cargo.id}`;
      const shareData = {
        title: `Cargo Tracking - ${cargo.trackingNumber}`,
        text: `Track cargo ${cargo.trackingNumber} from ${cargo.origin} to ${cargo.destination}`,
        url: cargoUrl,
      };

      try {
        if (navigator.share) {
          await navigator.share(shareData);
        } else {
          // Fallback: copy to clipboard
          await navigator.clipboard.writeText(cargoUrl);
          toast.success("Cargo tracking URL copied to clipboard");
        }
      } catch (error) {
        console.error("Error sharing:", error);
        toast.error("Failed to share cargo information");
      }
    };

    const handleCopyUrl = async () => {
      if (!cargo) return;

      const cargoUrl = `${window.location.origin}/cargo-management/${cargo.id}`;
      try {
        await navigator.clipboard.writeText(cargoUrl);
        toast.success("Cargo tracking URL copied to clipboard");
      } catch (error) {
        console.error("Error copying URL:", error);
        toast.error("Failed to copy URL");
      }
    };

    if (!cargo) return null;

    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <QrCode className="h-5 w-5" />
              QR Code - {cargo.trackingNumber}
            </DialogTitle>
            <DialogDescription>
              Generate QR code for cargo tracking and sharing
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Cargo Information */}
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Type:</span>
                <span className="text-sm font-medium">{cargo.type}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Route:</span>
                <span className="text-sm font-medium">
                  {cargo.origin} → {cargo.destination}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Status:</span>
                <span className="text-sm font-medium">{cargo.status}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Weight:</span>
                <span className="text-sm font-medium">{cargo.weight}</span>
              </div>
            </div>

            {/* QR Code Display */}
            <div className="flex justify-center">
              {isGenerating ? (
                <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-300 rounded-lg">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-gray-500">
                      Generating QR Code...
                    </p>
                  </div>
                </div>
              ) : qrCodeDataUrl ? (
                <div className="border-2 border-gray-200 rounded-lg p-4">
                  <img
                    src={qrCodeDataUrl}
                    alt={`QR Code for ${cargo.trackingNumber}`}
                    className="w-64 h-64 object-contain"
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-300 rounded-lg">
                  <p className="text-sm text-gray-500">
                    Failed to generate QR code
                  </p>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrint}
                disabled={!qrCodeDataUrl}
                className="flex-1"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleShare}
                disabled={!cargo}
                className="flex-1"
              >
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyUrl}
                disabled={!cargo}
                className="flex-1"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy URL
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }
);

QRCodeDialog.displayName = "QRCodeDialog";
