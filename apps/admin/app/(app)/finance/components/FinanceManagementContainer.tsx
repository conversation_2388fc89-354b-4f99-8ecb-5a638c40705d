"use client";

import { useRBAC } from "@/lib/hooks/useRBAC";
import { useFinanceManagement } from "../hooks/useFinanceManagement";
import { type LedgerWithStats } from "@/lib/logistics";
import { type TransactionWithAssociations } from "@/lib/logistics/operations/transactions";
import { type ColumnFilter } from "@/components/ui/filter-panel";

// Components
import { FinanceManagementHeader } from "./FinanceManagementHeader";
import { FinanceStatistics } from "./FinanceStatistics";
import { FinanceTabs } from "./FinanceTabs";
import { FinanceDialogs } from "./FinanceDialogs";
import { BulkTaskCreateDialog } from "@/components/ui/bulk-task-create";

// Types
export interface FinanceStats {
  totalRevenue: number;
  monthlyExpenses: number;
  profitMargin: number;
  pendingInvoices: number;
  pendingCount: number;
  totalLedgers: number;
  activeLedgers: number;
  pendingTransactions: number;
}

export interface FinanceManagementState {
  // View state
  activeTab:
    | "overview"
    | "ledgers"
    | "transactions"
    | "reports"
    | "cost-center";
  viewMode: "cards" | "table";
  searchTerm: string;
  filterStatus: string;
  filterCategory: string; // Add separate category filter
  loading: boolean;
  refreshing: boolean;

  // Pagination state
  currentPage: number;
  itemsPerPage: number;

  // Data state
  ledgers: LedgerWithStats[];
  transactions: TransactionWithAssociations[];
  financeStats: FinanceStats;

  // Ledger dialog state
  isCreateLedgerDialogOpen: boolean;
  isEditLedgerDialogOpen: boolean;
  isViewLedgerDialogOpen: boolean;
  isDeleteLedgerDialogOpen: boolean;
  selectedLedger: Ledger | null;

  // Transaction dialog state
  isCreateTransactionDialogOpen: boolean;
  isEditTransactionDialogOpen: boolean;
  isViewTransactionDialogOpen: boolean;
  isDeleteTransactionDialogOpen: boolean;
  selectedTransaction: TransactionWithAssociations | null;

  // Filter state
  columnFilters: ColumnFilter[];

  // Checkbox state
  selectedLedgers: Set<string>;
  selectedTransactions: Set<string>;

  // Bulk task creation state
  isBulkTaskDialogOpen: boolean;
  bulkTaskEntityType: "ledgers" | "transactions" | null;
}

/**
 * Main container component for Finance Management
 *
 * This component manages all the state and business logic for the finance management page.
 * It follows the container/presenter pattern for better separation of concerns.
 */
export function FinanceManagementContainer() {
  const { shouldShowCreateButton } = useRBAC();

  // Use custom hook for all finance management logic
  const {
    state,
    filteredLedgers,
    filteredTransactions,
    filteredFinanceStats,
    hasActiveFilters,
    handleRefresh,
    handleLedgerAction,
    handleTransactionAction,
    handleDialogClose,
    handleLedgerMutation,
    handleBulkLedgerStatusUpdate,
    handleBulkLedgerDelete,
    handleBulkTransactionStatusUpdate,
    handleBulkTransactionDelete,
    handleClearLedgerSelections,
    handleClearTransactionSelections,
    updateState,
    handleBulkCreateTasks,
    isBulkTaskDialogOpen,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    handleTasksCreated,
  } = useFinanceManagement();

  return (
    <div className="p-6 space-y-8">
      <FinanceManagementHeader
        loading={state.loading}
        shouldShowCreateButton={shouldShowCreateButton("ledgers")}
        onRefresh={handleRefresh}
        onCreateLedger={() => handleLedgerAction("create")}
      />

      <div className="space-y-8">
        <FinanceStatistics
          financeStats={state.financeStats}
          filteredFinanceStats={filteredFinanceStats}
          ledgers={state.ledgers}
          hasActiveFilters={hasActiveFilters}
          loading={state.loading}
        />

        <FinanceTabs
          activeTab={state.activeTab}
          viewMode={state.viewMode}
          searchTerm={state.searchTerm}
          filterStatus={state.filterStatus}
          filterCategory={state.filterCategory}
          columnFilters={state.columnFilters}
          loading={state.loading}
          filteredLedgers={filteredLedgers}
          filteredTransactions={filteredTransactions}
          currentPage={state.currentPage}
          itemsPerPage={state.itemsPerPage}
          onTabChange={(
            tab:
              | "overview"
              | "ledgers"
              | "transactions"
              | "reports"
              | "cost-center"
          ) => updateState({ activeTab: tab })}
          onViewModeChange={(mode: "cards" | "table") =>
            updateState({ viewMode: mode })
          }
          onSearchChange={(term: string) => updateState({ searchTerm: term })}
          onFilterStatusChange={(status: string) =>
            updateState({ filterStatus: status })
          }
          onFilterCategoryChange={(category: string) =>
            updateState({ filterCategory: category })
          }
          onColumnFiltersChange={(filters: ColumnFilter[]) =>
            updateState({ columnFilters: filters })
          }
          onPageChange={(page: number) => updateState({ currentPage: page })}
          onRefresh={handleRefresh}
          onLedgerAction={handleLedgerAction}
          onTransactionAction={handleTransactionAction}
          selectedLedgers={state.selectedLedgers}
          setSelectedLedgers={(items: Set<string>) =>
            updateState({ selectedLedgers: items })
          }
          selectedTransactions={state.selectedTransactions}
          setSelectedTransactions={(items: Set<string>) =>
            updateState({ selectedTransactions: items })
          }
          onBulkLedgerStatusUpdate={handleBulkLedgerStatusUpdate}
          onBulkLedgerDelete={handleBulkLedgerDelete}
          onBulkTransactionStatusUpdate={handleBulkTransactionStatusUpdate}
          onBulkTransactionDelete={handleBulkTransactionDelete}
          onBulkCreateTasks={handleBulkCreateTasks}
          onClearLedgerSelections={handleClearLedgerSelections}
          onClearTransactionSelections={handleClearTransactionSelections}
        />
      </div>

      {/* Bulk Task Create Dialog */}
      <BulkTaskCreateDialog
        isOpen={isBulkTaskDialogOpen}
        onClose={() => setIsBulkTaskDialogOpen(false)}
        onTasksCreated={handleTasksCreated}
        selectedItems={selectedItemsForTasks}
        associatedTable={state.bulkTaskEntityType || "ledgers"}
        title={`Create Tasks for Selected ${state.bulkTaskEntityType === "transactions" ? "Transactions" : "Ledgers"}`}
      />

      <FinanceDialogs
        state={state}
        onClose={handleDialogClose}
        onLedgerMutation={handleLedgerMutation}
        onTransactionMutation={handleRefresh}
      />
    </div>
  );
}
