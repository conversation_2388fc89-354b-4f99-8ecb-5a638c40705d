"use client";

import { memo } from "react";
import {
  DollarSign,
  <PERSON><PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  FileText,
  CheckCircle2,
} from "lucide-react";
import { Listing } from "@/modules/listing";
import { type FinanceStats } from "./FinanceManagementContainer";

interface FinanceStatisticsProps {
  financeStats: FinanceStats;
  filteredFinanceStats: FinanceStats;
  hasActiveFilters: boolean;
  activeTab:
    | "overview"
    | "ledgers"
    | "transactions"
    | "reports"
    | "cost-center";
  loading: boolean;
}

/**
 * Finance Statistics Component
 *
 * Displays key financial metrics in a grid of stat cards.
 * Uses filtered data when filters are active, similar to invoice workflow pattern.
 * Uses the standardized StatCard component from the listing module.
 */
export const FinanceStatistics = memo<FinanceStatisticsProps>(
  ({
    financeStats,
    filteredFinanceStats,
    hasActiveFilters,
    activeTab,
    loading,
  }) => {
    // Use filtered stats when filters are active, otherwise use original stats
    const displayStats = hasActiveFilters ? filteredFinanceStats : financeStats;
    return (
      <Listing.Statistics columns="grid-cols-5">
        <Listing.StatCard
          icon={DollarSign}
          name="Total Revenue"
          value={displayStats.totalRevenue}
          valueType="dollar"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered{" "}
                {activeTab === "transactions" ? "Transactions" : "Ledgers"}
              </span>
            ) : (
              <div className="flex items-center gap-1 text-green-600">
                <CheckCircle2 className="h-3 w-3" />
                {activeTab === "transactions"
                  ? "From transactions"
                  : "From ledgers"}
              </div>
            )
          }
          color="primary"
          loading={loading}
        />

        <Listing.StatCard
          icon={BarChart3}
          name="Total Expenses"
          value={displayStats.monthlyExpenses}
          valueType="dollar"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered{" "}
                {activeTab === "transactions" ? "Transactions" : "Ledgers"}
              </span>
            ) : (
              <div className="flex items-center gap-1 text-red-500">
                <CheckCircle2 className="h-3 w-3" />
                {activeTab === "transactions"
                  ? "From transactions"
                  : "From ledgers"}
              </div>
            )
          }
          color="blue"
          loading={loading}
        />

        <Listing.StatCard
          icon={PieChart}
          name="Profit Margin"
          value={displayStats.profitMargin}
          valueType="percent"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <div className="flex items-center gap-1 text-green-600">
                <CheckCircle2 className="h-3 w-3" />
                Calculated
              </div>
            )
          }
          color="green"
          loading={loading}
        />

        <Listing.StatCard
          icon={FileText}
          name="Active Ledgers"
          value={displayStats.activeLedgers}
          valueType="number"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span className="text-xs text-purple-600 flex items-center gap-1">
                <CheckCircle2 className="h-3 w-3" />
                {displayStats.totalLedgers} total
              </span>
            )
          }
          color="purple"
          loading={loading}
        />

        <Listing.StatCard
          icon={BarChart3}
          name="Total Transactions"
          value={displayStats.totalTransactions || 0}
          valueType="number"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span className="text-xs text-indigo-600 flex items-center gap-1">
                <CheckCircle2 className="h-3 w-3" />
                All transactions
              </span>
            )
          }
          color="blue"
          loading={loading}
        />
      </Listing.Statistics>
    );
  }
);

FinanceStatistics.displayName = "FinanceStatistics";
