"use client";

import { memo } from "react";
import {
  <PERSON>Sign,
  <PERSON>Chart3,
  <PERSON><PERSON><PERSON>,
  FileText,
  TrendingUp,
  Loader2,
} from "lucide-react";
import { Listing } from "@/modules/listing";
import { type LedgerWithStats } from "@/lib/logistics";
import { type FinanceStats } from "./FinanceManagementContainer";

interface FinanceStatisticsProps {
  financeStats: FinanceStats;
  ledgers: LedgerWithStats[];
  loading: boolean;
}

/**
 * Finance Statistics Component
 *
 * Displays key financial metrics in a grid of stat cards.
 * Uses the standardized StatCard component from the listing module.
 */
export const FinanceStatistics = memo<FinanceStatisticsProps>(
  ({ financeStats, ledgers, loading }) => {
    return (
      <Listing.Statistics columns="grid-cols-5">
        <Listing.StatCard
          icon={DollarSign}
          name="Total Revenue"
          value={financeStats.totalRevenue}
          valueType="dollar"
          caption={
            <div className="flex items-center gap-1 text-green-600">
              <TrendingUp className="h-3 w-3" />
              Real-time data
            </div>
          }
          color="primary"
          loading={loading}
        />

        <Listing.StatCard
          icon={BarChart3}
          name="Total Expenses"
          value={financeStats.monthlyExpenses}
          valueType="dollar"
          caption={
            <div className="flex items-center gap-1 text-red-500">
              <TrendingUp className="h-3 w-3 rotate-180" />
              All time
            </div>
          }
          color="blue"
          loading={loading}
        />

        <Listing.StatCard
          icon={PieChart}
          name="Profit Margin"
          value={financeStats.profitMargin}
          valueType="percent"
          caption={
            <div className="flex items-center gap-1 text-green-600">
              <TrendingUp className="h-3 w-3" />
              Calculated
            </div>
          }
          color="green"
          loading={loading}
        />

        <Listing.StatCard
          icon={FileText}
          name="Active Ledgers"
          value={financeStats.activeLedgers}
          valueType="number"
          caption={`${financeStats.totalLedgers} total`}
          color="purple"
          loading={loading}
        />

        <Listing.StatCard
          icon={DollarSign}
          name="Pending Invoices"
          value={financeStats.pendingInvoices}
          valueType="dollar"
          caption={`${financeStats.pendingCount} invoices`}
          color="amber"
          loading={loading}
        />
      </Listing.Statistics>
    );
  }
);

FinanceStatistics.displayName = "FinanceStatistics";
