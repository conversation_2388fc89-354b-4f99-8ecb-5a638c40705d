"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAppSelector } from "@/store/hooks";
import {
  ledgerService,
  transactionService,
  invoiceService,
  type LedgerWithStats,
} from "@/lib/logistics";
import { type TransactionWithAssociations } from "@/lib/logistics/operations/transactions";
import {
  type FinanceStats,
  type FinanceManagementState,
} from "../components/FinanceManagementContainer";
import { toast } from "sonner";

/**
 * Custom hook for finance management logic
 *
 * Encapsulates all business logic, state management, and side effects
 * for the finance management feature. Follows React best practices with
 * proper memoization and optimization.
 */
export function useFinanceManagement() {
  const { user: authUser } = useAppSelector((state) => state.auth);
  const router = useRouter();

  // Initial state
  const [state, setState] = useState<FinanceManagementState>({
    activeTab: "ledgers",
    viewMode: "table", // Default to table as per user preference
    searchTerm: "",
    filterStatus: "all",
    filterCategory: "all", // Add separate category filter
    loading: true,
    refreshing: false,
    // Pagination state
    currentPage: 1,
    itemsPerPage: 10,
    ledgers: [],
    transactions: [],
    financeStats: {
      totalRevenue: 0,
      monthlyExpenses: 0,
      profitMargin: 0,
      pendingInvoices: 0,
      pendingCount: 0,
      totalLedgers: 0,
      activeLedgers: 0,
      pendingTransactions: 0,
    },
    isCreateLedgerDialogOpen: false,
    isEditLedgerDialogOpen: false,
    isViewLedgerDialogOpen: false,
    isDeleteLedgerDialogOpen: false,
    selectedLedger: null,
    isCreateTransactionDialogOpen: false,
    isEditTransactionDialogOpen: false,
    isViewTransactionDialogOpen: false,
    isDeleteTransactionDialogOpen: false,
    selectedTransaction: null,
    columnFilters: [],
    selectedLedgers: new Set<string>(),
    selectedTransactions: new Set<string>(),
    isBulkTaskDialogOpen: false,
    bulkTaskEntityType: null,
  });

  // Fetch finance data
  const fetchFinanceData = useCallback(
    async (refresh = false) => {
      if (!authUser?.accountId) return;

      try {
        if (refresh) {
          setState((prev) => ({ ...prev, refreshing: true }));
        } else {
          setState((prev) => ({ ...prev, loading: true }));
        }

        const [
          ledgersResult,
          transactionsResult,
          transactionStatsResult,
          invoiceStatsResult,
        ] = await Promise.all([
          ledgerService.getAllLedgers({
            limit: 100,
            // Temporarily remove account filtering to test
            // filters: { account_id: authUser.accountId },
          }),
          transactionService.getAllTransactionsWithLedgers({
            limit: 100,
            // Temporarily remove account filtering to test
            // filters: { account_id: authUser.accountId },
          }),
          transactionService.getTransactionStats(),
          invoiceService.getInvoiceStats(),
        ]);

        if (ledgersResult.success && ledgersResult.data) {
          const ledgers = ledgersResult.data;
          const transactions = transactionsResult.success
            ? transactionsResult.data || []
            : [];

          console.log("Finance Data Fetched:", {
            ledgersCount: ledgers.length,
            transactionsCount: transactions.length,
            accountId: authUser.accountId,
            ledgersResult: ledgersResult,
            transactionsResult: transactionsResult,
            sampleLedger: ledgers[0],
            sampleTransaction: transactions[0],
          });

          // Calculate statistics
          const stats = calculateFinanceStats(
            ledgers,
            transactionStatsResult.data,
            invoiceStatsResult.data
          );

          setState((prev) => ({
            ...prev,
            ledgers,
            transactions,
            financeStats: stats,
            loading: false,
            refreshing: false,
          }));
        } else {
          console.error("Failed to fetch ledgers:", ledgersResult.error);
          console.error(
            "Failed to fetch transactions:",
            transactionsResult.error
          );
          setState((prev) => ({ ...prev, loading: false, refreshing: false }));
        }
      } catch (error) {
        console.error("Error fetching finance data:", error);
        setState((prev) => ({ ...prev, loading: false, refreshing: false }));
      }
    },
    [authUser?.accountId]
  );

  // Calculate finance statistics
  const calculateFinanceStats = useCallback(
    (
      ledgers: LedgerWithStats[],
      transactionStats: any,
      invoiceStats: any
    ): FinanceStats => {
      const totalLedgers = ledgers.length;
      const activeLedgers = ledgers.filter((l) => l.status === "ACTIVE").length;

      const totalRevenue = transactionStats?.totalIncome || 0;
      const monthlyExpenses = transactionStats?.totalExpenses || 0;
      const profitMargin =
        totalRevenue > 0
          ? ((totalRevenue - monthlyExpenses) / totalRevenue) * 100
          : 0;

      const pendingInvoices = invoiceStats?.pendingAmount || 0;
      const pendingCount = invoiceStats?.pendingCount || 0;

      // Calculate pending transactions from current transactions state
      const pendingTransactions = state.transactions.filter(
        (transaction) => transaction.status === "PENDING"
      ).length;

      return {
        totalRevenue,
        monthlyExpenses,
        profitMargin,
        pendingInvoices,
        pendingCount,
        totalLedgers,
        activeLedgers,
        pendingTransactions,
      };
    },
    [state.transactions]
  );

  // Helper function to check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return (
      state.filterStatus !== "all" ||
      state.filterCategory !== "all" ||
      !!state.searchTerm ||
      state.columnFilters.length > 0
    );
  }, [
    state.filterStatus,
    state.filterCategory,
    state.searchTerm,
    state.columnFilters,
  ]);

  // Filtered ledgers based on search and filters
  const filteredLedgers = useMemo(() => {
    return state.ledgers.filter((ledger) => {
      // Search filter
      const searchMatch =
        !state.searchTerm ||
        ledger.name?.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
        ledger.tags?.some((tag: string) =>
          tag.toLowerCase().includes(state.searchTerm.toLowerCase())
        ) ||
        ledger.books?.some((book) =>
          book.toLocaleLowerCase().includes(state.searchTerm.toLowerCase())
        );

      // Status filter
      const statusMatch =
        state.filterStatus === "all" ||
        ledger.status?.toLowerCase() === state.filterStatus.toLowerCase();

      // Category filter
      const categoryMatch =
        state.filterCategory === "all" ||
        (ledger.category?.toLowerCase() || "uncategorized") ===
          state.filterCategory.toLowerCase();

      // Column filters
      const columnMatch = state.columnFilters.every((filter) => {
        const value = ledger[filter.column as keyof LedgerWithStats];
        if (value === null || value === undefined) return false;
        return String(value).toLowerCase().includes(filter.value.toLowerCase());
      });

      return searchMatch && statusMatch && categoryMatch && columnMatch;
    });
  }, [
    state.ledgers,
    state.searchTerm,
    state.filterStatus,
    state.filterCategory,
    state.columnFilters,
  ]);

  // Filtered transactions based on search and filters
  const filteredTransactions = useMemo(() => {
    return state.transactions.filter((transaction) => {
      // Search filter
      const searchMatch =
        !state.searchTerm ||
        transaction.name
          ?.toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        transaction.context
          ?.toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        transaction.ledger?.name
          ?.toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        transaction.tags?.some((tag: string) =>
          tag.toLowerCase().includes(state.searchTerm.toLowerCase())
        );

      // Status filter
      const statusMatch =
        state.filterStatus === "all" ||
        transaction.status?.toLowerCase() === state.filterStatus.toLowerCase();

      // Column filters
      const columnMatch = state.columnFilters.every((filter) => {
        const value =
          transaction[filter.column as keyof TransactionWithAssociations];
        if (value === null || value === undefined) return false;
        return String(value).toLowerCase().includes(filter.value.toLowerCase());
      });

      return searchMatch && statusMatch && columnMatch;
    });
  }, [
    state.transactions,
    state.searchTerm,
    state.filterStatus,
    state.columnFilters,
  ]);

  // Calculate filtered stats for transactions (matching finance page logic)
  const filteredStats = useMemo(() => {
    const totalTransactions = filteredTransactions.length;

    // Calculate pending transactions as sum of amounts (not count)
    const pendingTransactions = filteredTransactions
      .filter((transaction) => transaction.status === "PENDING")
      .reduce((sum, t) => sum + Math.abs(t.amount || 0), 0);

    const activeTransactions = filteredTransactions.filter(
      (transaction) => transaction.status === "ACTIVE"
    ).length;
    const completedTransactions = filteredTransactions.filter(
      (transaction) => transaction.status === "COMPLETED"
    ).length;

    // Calculate totals using amount-based logic (like finance page)
    // Credit transactions (positive amounts) are income, Debit transactions (negative amounts) are expenses
    const totalIncome = filteredTransactions
      .filter((t) => t.amount >= 0) // CREDIT transactions (positive amounts)
      .reduce((sum, t) => sum + (t.amount || 0), 0);
    const totalExpenses = Math.abs(
      filteredTransactions
        .filter((t) => t.amount < 0) // DEBIT transactions (negative amounts)
        .reduce((sum, t) => sum + (t.amount || 0), 0)
    );
    const netAmount = totalIncome - totalExpenses;

    return {
      totalTransactions,
      pendingTransactions,
      activeTransactions,
      completedTransactions,
      totalIncome,
      totalExpenses,
      netAmount,
    };
  }, [filteredTransactions]);

  // Calculate filtered finance stats based on filtered data
  // Use OR logic: aggregate from ledgers OR transactions based on active tab
  const filteredFinanceStats = useMemo(() => {
    const totalLedgers = filteredLedgers.length;
    const activeLedgers = filteredLedgers.filter(
      (l) => l.status === "ACTIVE"
    ).length;

    let totalRevenue = 0;
    let monthlyExpenses = 0;
    let totalTransactionCount = 0;

    if (state.activeTab === "transactions") {
      // When on transactions tab, aggregate from filtered transactions
      totalRevenue = filteredTransactions
        .filter((t) => t.type === "CREDIT")
        .reduce((sum, transaction) => sum + (transaction.amount || 0), 0);

      monthlyExpenses = filteredTransactions
        .filter((t) => t.type === "DEBIT")
        .reduce(
          (sum, transaction) => sum + Math.abs(transaction.amount || 0),
          0
        );

      totalTransactionCount = filteredTransactions.length;
    } else {
      // When on ledgers tab (or other tabs), aggregate from filtered ledgers
      // Each ledger already has calculated totalRevenue and totalExpenses from its transactions
      totalRevenue = filteredLedgers.reduce(
        (sum, ledger) => sum + (ledger.totalRevenue || 0),
        0
      );
      monthlyExpenses = filteredLedgers.reduce(
        (sum, ledger) => sum + (ledger.totalExpenses || 0),
        0
      );

      // Also aggregate total transaction count from filtered ledgers
      totalTransactionCount = filteredLedgers.reduce(
        (sum, ledger) => sum + (ledger.totalTransactions || 0),
        0
      );
    }

    const profitMargin =
      totalRevenue > 0
        ? ((totalRevenue - monthlyExpenses) / totalRevenue) * 100
        : 0;

    // For pending invoices, we'll use the original calculation since invoices aren't filtered here
    // TODO: In the future, we could filter invoices based on associated ledgers
    const pendingInvoices = state.financeStats.pendingInvoices;
    const pendingCount = state.financeStats.pendingCount;

    // Use filtered transaction stats for pending transactions
    const pendingTransactions = filteredStats.pendingTransactions;

    // Debug logging to verify aggregation
    console.log("Filtered Finance Stats Debug:", {
      activeTab: state.activeTab,
      aggregationSource:
        state.activeTab === "transactions" ? "transactions" : "ledgers",
      filteredLedgersCount: filteredLedgers.length,
      filteredTransactionsCount: filteredTransactions.length,
      ...(state.activeTab === "transactions"
        ? {
            transactionBreakdown: {
              creditTransactions: filteredTransactions.filter(
                (t) => t.type === "CREDIT"
              ).length,
              debitTransactions: filteredTransactions.filter(
                (t) => t.type === "DEBIT"
              ).length,
              totalCredits: filteredTransactions
                .filter((t) => t.type === "CREDIT")
                .reduce((sum, t) => sum + (t.amount || 0), 0),
              totalDebits: filteredTransactions
                .filter((t) => t.type === "DEBIT")
                .reduce((sum, t) => sum + Math.abs(t.amount || 0), 0),
            },
          }
        : {
            ledgerBreakdown: {
              ledgerRevenues: filteredLedgers.map((l) => ({
                name: l.name,
                revenue: l.totalRevenue,
                transactions: l.totalTransactions,
              })),
              ledgerExpenses: filteredLedgers.map((l) => ({
                name: l.name,
                expenses: l.totalExpenses,
                transactions: l.totalTransactions,
              })),
            },
          }),
      aggregatedRevenue: totalRevenue,
      aggregatedExpenses: monthlyExpenses,
      totalTransactionCount,
      profitMargin,
    });

    return {
      totalRevenue,
      monthlyExpenses,
      profitMargin,
      pendingInvoices,
      pendingCount,
      totalLedgers,
      activeLedgers,
      pendingTransactions,
      totalTransactions: totalTransactionCount,
    };
  }, [
    filteredLedgers,
    filteredTransactions,
    filteredStats,
    state.financeStats,
    state.activeTab,
  ]);

  // Load data on mount
  useEffect(() => {
    fetchFinanceData();
  }, [fetchFinanceData]);

  // Reset pagination when filters change
  useEffect(() => {
    setState((prev) => ({ ...prev, currentPage: 1 }));
  }, [state.searchTerm, state.filterStatus, state.filterCategory]);

  // Event handlers
  const handleRefresh = useCallback(async () => {
    await fetchFinanceData(true);
  }, [fetchFinanceData]);

  const handleLedgerAction = useCallback(
    (action: string, ledger?: LedgerWithStats) => {
      switch (action) {
        case "create":
          setState((prev) => ({ ...prev, isCreateLedgerDialogOpen: true }));
          break;
        case "view":
          setState((prev) => ({
            ...prev,
            selectedLedger: ledger!,
            isViewLedgerDialogOpen: true,
          }));
          break;
        case "edit":
          setState((prev) => ({
            ...prev,
            selectedLedger: ledger!,
            isEditLedgerDialogOpen: true,
          }));
          break;
        case "delete":
          setState((prev) => ({
            ...prev,
            selectedLedger: ledger!,
            isDeleteLedgerDialogOpen: true,
          }));
          break;
        case "openLedger":
          if (ledger?.id) {
            router.push(`/finance/${ledger.id}`);
          } else {
            toast.error("Ledger ID is required to open ledger");
          }
          break;
      }
    },
    [router]
  );

  const handleTransactionAction = useCallback(
    (action: string, transaction?: TransactionWithAssociations) => {
      switch (action) {
        case "create":
          setState((prev) => ({
            ...prev,
            isCreateTransactionDialogOpen: true,
          }));
          break;
        case "view":
          setState((prev) => ({
            ...prev,
            selectedTransaction: transaction!,
            isViewTransactionDialogOpen: true,
          }));
          break;
        case "edit":
          setState((prev) => ({
            ...prev,
            selectedTransaction: transaction!,
            isEditTransactionDialogOpen: true,
          }));
          break;
        case "delete":
          setState((prev) => ({
            ...prev,
            selectedTransaction: transaction!,
            isDeleteTransactionDialogOpen: true,
          }));
          break;
      }
    },
    []
  );

  const handleDialogClose = useCallback((dialogType: string) => {
    setState((prev) => ({
      ...prev,
      [`is${dialogType}Open`]: false,
      selectedLedger: null,
      selectedTransaction: null,
    }));
  }, []);

  const handleLedgerMutation = useCallback(async () => {
    await fetchFinanceData(true);
  }, [fetchFinanceData]);

  // Bulk actions handlers for ledgers
  const handleBulkLedgerStatusUpdate = useCallback(
    async (status: string) => {
      const selectedIds = Array.from(state.selectedLedgers);
      if (selectedIds.length === 0) {
        console.warn("No ledgers selected for bulk status update");
        return;
      }

      try {
        // Update each selected ledger's status
        const updatePromises = selectedIds.map((ledgerId) =>
          ledgerService.update(ledgerId, { status: status as any })
        );

        const updateResults = await Promise.all(updatePromises);
        const successfulUpdates = updateResults.filter(
          (result) => result.success
        ).length;
        const failedUpdates = updateResults.length - successfulUpdates;

        if (successfulUpdates > 0) {
          console.log(
            `Successfully updated ${successfulUpdates} ledger(s) to ${status}`,
            failedUpdates > 0 ? `${failedUpdates} update(s) failed` : ""
          );

          // Clear selection after successful action
          setState((prev) => ({ ...prev, selectedLedgers: new Set() }));

          // Refresh data to show updated statuses
          await fetchFinanceData(true);
        } else {
          console.error("Failed to update any ledger status");
        }
      } catch (error) {
        console.error("Error in bulk ledger status update:", error);
      }
    },
    [state.selectedLedgers]
  );

  const handleBulkLedgerDelete = useCallback(() => {
    const selectedIds = Array.from(state.selectedLedgers);
    if (selectedIds.length === 0) return;

    console.log(`Deleting ${selectedIds.length} ledgers:`, selectedIds);
    // TODO: Implement bulk ledger delete logic
    setState((prev) => ({ ...prev, selectedLedgers: new Set() })); // Clear selection after action
  }, [state.selectedLedgers]);

  // Bulk actions handlers for transactions
  const handleBulkTransactionStatusUpdate = useCallback(
    async (status: string) => {
      const selectedIds = Array.from(state.selectedTransactions);
      if (selectedIds.length === 0) {
        console.warn("No transactions selected for bulk status update");
        return;
      }

      try {
        // Update each selected transaction's status
        const updatePromises = selectedIds.map((transactionId) =>
          transactionService.update(transactionId, { status: status as any })
        );

        const updateResults = await Promise.all(updatePromises);
        const successfulUpdates = updateResults.filter(
          (result) => result.success
        ).length;
        const failedUpdates = updateResults.length - successfulUpdates;

        if (successfulUpdates > 0) {
          console.log(
            `Successfully updated ${successfulUpdates} transaction(s) to ${status}`,
            failedUpdates > 0 ? `${failedUpdates} update(s) failed` : ""
          );

          // Clear selection after successful action
          setState((prev) => ({ ...prev, selectedTransactions: new Set() }));

          // Refresh data to show updated statuses
          await fetchFinanceData(true);
        } else {
          console.error("Failed to update any transaction status");
        }
      } catch (error) {
        console.error("Error in bulk transaction status update:", error);
      }
    },
    [state.selectedTransactions]
  );

  const handleBulkTransactionDelete = useCallback(() => {
    const selectedIds = Array.from(state.selectedTransactions);
    if (selectedIds.length === 0) return;

    console.log(`Deleting ${selectedIds.length} transactions:`, selectedIds);
    // TODO: Implement bulk transaction delete logic
    setState((prev) => ({ ...prev, selectedTransactions: new Set() })); // Clear selection after action
  }, [state.selectedTransactions]);

  // State update handlers
  const updateState = useCallback(
    (updates: Partial<FinanceManagementState>) => {
      setState((prev) => ({ ...prev, ...updates }));
    },
    []
  );

  // Clear selections handlers
  const handleClearLedgerSelections = useCallback(() => {
    setState((prev) => ({ ...prev, selectedLedgers: new Set() }));
    toast.success("Ledger selections cleared");
  }, []);

  const handleClearTransactionSelections = useCallback(() => {
    setState((prev) => ({ ...prev, selectedTransactions: new Set() }));
    toast.success("Transaction selections cleared");
  }, []);

  // Bulk task creation handlers
  const handleBulkCreateTasks = useCallback(
    (entityType: "ledgers" | "transactions") => {
      const selectedItems =
        entityType === "ledgers"
          ? state.selectedLedgers
          : state.selectedTransactions;
      if (selectedItems.size === 0) {
        toast.error(`Please select ${entityType} to create tasks for`);
        return;
      }
      setState((prev) => ({
        ...prev,
        isBulkTaskDialogOpen: true,
        bulkTaskEntityType: entityType,
      }));
    },
    [state.selectedLedgers, state.selectedTransactions]
  );

  const setIsBulkTaskDialogOpen = useCallback((isOpen: boolean) => {
    setState((prev) => ({
      ...prev,
      isBulkTaskDialogOpen: isOpen,
      bulkTaskEntityType: isOpen ? prev.bulkTaskEntityType : null,
    }));
  }, []);

  const selectedItemsForTasks = useMemo(() => {
    if (!state.bulkTaskEntityType) return [];

    if (state.bulkTaskEntityType === "ledgers") {
      return Array.from(state.selectedLedgers).map((ledgerId) => {
        const ledger = state.ledgers.find((l) => l.id === ledgerId);
        return {
          id: ledgerId,
          name: ledger?.name || "Unknown Ledger",
          identifier: ledger?.code || undefined,
        };
      });
    } else {
      return Array.from(state.selectedTransactions).map((transactionId) => {
        const transaction = state.transactions.find(
          (t) => t.id === transactionId
        );
        return {
          id: transactionId,
          name: transaction?.name || "Unknown Transaction",
          identifier: transaction?.id,
        };
      });
    }
  }, [
    state.selectedLedgers,
    state.selectedTransactions,
    state.ledgers,
    state.transactions,
    state.bulkTaskEntityType,
  ]);

  const handleTasksCreated = useCallback(() => {
    // Clear selections and refresh data
    if (state.bulkTaskEntityType === "ledgers") {
      setState((prev) => ({ ...prev, selectedLedgers: new Set() }));
    } else {
      setState((prev) => ({ ...prev, selectedTransactions: new Set() }));
    }
    fetchFinanceData(true);
  }, [state.bulkTaskEntityType, fetchFinanceData]);

  return {
    state,
    filteredLedgers,
    filteredTransactions,
    filteredStats,
    filteredFinanceStats,
    hasActiveFilters,
    handleRefresh,
    handleLedgerAction,
    handleTransactionAction,
    handleDialogClose,
    handleLedgerMutation,
    handleBulkLedgerStatusUpdate,
    handleBulkLedgerDelete,
    handleBulkTransactionStatusUpdate,
    handleBulkTransactionDelete,
    handleClearLedgerSelections,
    handleClearTransactionSelections,
    updateState,
    handleBulkCreateTasks,
    isBulkTaskDialogOpen: state.isBulkTaskDialogOpen,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    handleTasksCreated,
  };
}
