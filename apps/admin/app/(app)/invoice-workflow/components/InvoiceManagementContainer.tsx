"use client";

import { useRB<PERSON> } from "@/lib/hooks/useRBAC";
import { useInvoiceManagement } from "../hooks/useInvoiceManagement";
import { type InvoiceWithRelations } from "@/lib/logistics";
import { type ColumnFilter } from "@/components/ui/filter-panel";

// Components
import { InvoiceManagementHeader } from "./InvoiceManagementHeader";
import { InvoiceStatistics } from "./InvoiceStatistics";
import { InvoiceTabs } from "./InvoiceTabs";
import { InvoiceDialogs } from "./InvoiceDialogs";
import { BulkTaskCreateDialog } from "@/components/ui/bulk-task-create";

// Types
export interface InvoiceStats {
  totalInvoices: number;
  paidInvoicesCount: number;
  pendingInvoicesCount: number;
  overdueInvoicesCount: number;
  paidAmount: number;
  pendingAmount: number;
  overdueAmount: number;
}

export interface InvoiceManagementState {
  // View state
  activeTab: "overview" | "analytics";
  searchTerm: string;
  filterStatus: string;
  loading: boolean;

  // Data state
  invoices: InvoiceWithRelations[];
  invoiceStats: InvoiceStats;

  // Dialog state
  isNewInvoiceDrawerOpen: boolean;
  isViewInvoiceModalOpen: boolean;
  isEditInvoiceModalOpen: boolean;
  isPaymentLedgerDialogOpen: boolean;
  selectedInvoice: InvoiceWithRelations | null;

  // Filter state
  columnFilters: ColumnFilter[];

  // Bulk task creation state
  isBulkTaskDialogOpen: boolean;
}

/**
 * Main container component for Invoice Management
 *
 * This component manages all the state and business logic for the invoice management page.
 * It follows the container/presenter pattern for better separation of concerns.
 */
export function InvoiceManagementContainer() {
  const { shouldShowCreateButton } = useRBAC();

  // Use custom hook for all invoice management logic
  const {
    state,
    filteredInvoices,
    updateState,
    handleRefresh,
    handleInvoiceAction,
    handleDialogClose,
    handleInvoiceMutation,
    handleBulkStatusUpdate,
    handleBulkDelete,
    handleClearSelections,
    handleBulkCreateTasks,
    isBulkTaskDialogOpen,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    handleTasksCreated,
  } = useInvoiceManagement();

  return (
    <div className="flex flex-col gap-8 p-6">
      {/* Header */}
      <InvoiceManagementHeader
        loading={state.loading}
        shouldShowCreateButton={shouldShowCreateButton("invoices")}
        onRefresh={handleRefresh}
        onCreateInvoice={() => handleInvoiceAction("create")}
      />

      {/* Statistics */}
      <InvoiceStatistics
        invoiceStats={state.invoiceStats}
        invoices={state.invoices}
        loading={state.loading}
      />

      {/* Tabs */}
      <InvoiceTabs
        activeTab={state.activeTab}
        viewMode={state.viewMode}
        searchTerm={state.searchTerm}
        filterStatus={state.filterStatus}
        columnFilters={state.columnFilters}
        loading={state.loading}
        filteredInvoices={filteredInvoices}
        onTabChange={(tab) => updateState({ activeTab: tab })}
        onViewModeChange={(mode) => updateState({ viewMode: mode })}
        onSearchChange={(term) => updateState({ searchTerm: term })}
        onFilterStatusChange={(status) => updateState({ filterStatus: status })}
        onColumnFiltersChange={(filters) =>
          updateState({ columnFilters: filters })
        }
        onRefresh={handleRefresh}
        onInvoiceAction={handleInvoiceAction}
        selectedItems={state.selectedItems}
        setSelectedItems={(items: Set<string>) =>
          updateState({ selectedItems: items })
        }
        onBulkStatusUpdate={handleBulkStatusUpdate}
        onBulkDelete={handleBulkDelete}
        onBulkCreateTasks={handleBulkCreateTasks}
        onClearSelections={handleClearSelections}
        currentPage={state.currentPage}
        onPageChange={(page: number) => updateState({ currentPage: page })}
      />

      {/* Bulk Task Create Dialog */}
      <BulkTaskCreateDialog
        isOpen={isBulkTaskDialogOpen}
        onClose={() => setIsBulkTaskDialogOpen(false)}
        onTasksCreated={handleTasksCreated}
        selectedItems={selectedItemsForTasks}
        associatedTable="invoices"
        title="Create Tasks for Selected Invoices"
      />

      {/* Dialogs */}
      <InvoiceDialogs
        state={state}
        onDialogClose={handleDialogClose}
        onInvoiceMutation={handleInvoiceMutation}
      />
    </div>
  );
}
