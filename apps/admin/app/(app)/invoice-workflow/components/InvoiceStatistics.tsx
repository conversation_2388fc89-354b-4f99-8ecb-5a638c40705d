"use client";

import { memo } from "react";
import {
  <PERSON><PERSON>ext,
  CheckCircle,
  Clock,
  AlertTriangle,
  DollarSign,
  TrendingUp,
  BarChart3,
} from "lucide-react";
import { Listing } from "@/modules/listing";
import { type InvoiceStats } from "./InvoiceManagementContainer";
import { type InvoiceWithRelations } from "@/lib/logistics";

interface InvoiceStatisticsProps {
  invoiceStats: InvoiceStats;
  invoices: InvoiceWithRelations[];
  loading: boolean;
}

/**
 * Statistics component for Invoice Management
 *
 * Displays key metrics and KPIs for invoices in a grid layout.
 * Memoized to prevent unnecessary re-renders when parent state changes.
 */
export const InvoiceStatistics = memo<InvoiceStatisticsProps>(
  ({ invoiceStats, invoices, loading }) => {
    // Calculate average invoice value
    const averageInvoiceValue =
      invoiceStats.totalInvoices > 0
        ? (invoiceStats.paidAmount +
            invoiceStats.pendingAmount +
            invoiceStats.overdueAmount) /
          invoiceStats.totalInvoices
        : 0;

    // Calculate collection rate
    const collectionRate =
      invoiceStats.totalInvoices > 0
        ? (invoiceStats.paidInvoicesCount / invoiceStats.totalInvoices) * 100
        : 0;

    return (
      <Listing.Statistics columns="grid-cols-5">
        <Listing.StatCard
          icon={FileText}
          name="Total Invoices"
          value={invoiceStats.totalInvoices}
          valueType="number"
          caption={
            <span className="text-xs text-blue-600 flex items-center gap-1">
              <BarChart3 className="h-3 w-3" /> All invoices
            </span>
          }
          color="primary"
          loading={loading}
        />

        <Listing.StatCard
          icon={CheckCircle}
          name="Paid"
          value={invoiceStats.paidAmount}
          valueType="number"
          caption={
            <span className="text-xs text-green-600 flex items-center gap-1">
              ({invoiceStats.paidInvoicesCount}) Paid invoices
            </span>
          }
          color="green"
          loading={loading}
        />

        <Listing.StatCard
          icon={Clock}
          name="Pending"
          value={invoiceStats.pendingAmount}
          valueType="number"
          caption={
            <span className="text-xs text-amber-600 flex items-center gap-1">
              ({invoiceStats.pendingInvoicesCount}) Pending invoices
            </span>
          }
          color="amber"
          loading={loading}
        />

        <Listing.StatCard
          icon={AlertTriangle}
          name="Overdue"
          value={invoiceStats.overdueAmount}
          valueType="number"
          caption={
            <span className="text-xs text-red-600 flex items-center gap-1">
              ({invoiceStats.overdueInvoicesCount}) Overdue invoices
            </span>
          }
          color="red"
          loading={loading}
        />

        <Listing.StatCard
          icon={DollarSign}
          name="Avg Value"
          value={averageInvoiceValue}
          valueType="currency"
          caption={
            <span className="text-xs text-purple-600 flex items-center gap-1">
              <TrendingUp className="h-3 w-3" />
              {collectionRate.toFixed(1)}% collected
            </span>
          }
          color="purple"
          loading={loading}
        />
      </Listing.Statistics>
    );
  }
);

InvoiceStatistics.displayName = "InvoiceStatistics";
