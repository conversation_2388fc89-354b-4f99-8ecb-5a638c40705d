"use client";

import { useState, useEffect, useCallback } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useAppSelector } from "@/store/hooks";
import { invoiceService, type InvoiceWithRelations } from "@/lib/logistics";
import { ledgerService } from "@/lib/logistics/operations/ledgers";
import { transactionService } from "@/lib/logistics/operations/transactions";
import { cargoService } from "@/lib/logistics/operations/cargos";
import { type ColumnFilter } from "@/components/ui/filter-panel";
//
import {
  prepareInvoiceTemplateData,
  downloadPDF,
  generateInvoiceTemplate,
} from "@/lib/invoice-template-generator";
import { documentService } from "@/lib/logistics/operations/documents";
import {
  CustomerAlertsService,
  transformCustomerToContact,
} from "@/lib/customerAlerts";
import { toast } from "sonner";

// Constants
export const ITEMS_PER_PAGE = 10;

// Types
export interface InvoiceStats {
  totalInvoices: number;
  paidInvoicesCount: number;
  pendingInvoicesCount: number;
  overdueInvoicesCount: number;
  paidAmount: number;
  pendingAmount: number;
  overdueAmount: number;
}

export interface InvoiceManagementState {
  // View state
  activeTab: "overview" | "analytics";
  viewMode: "cards" | "table";
  searchTerm: string;
  filterStatus: string;
  currentPage: number;
  loading: boolean;
  refreshing: boolean;

  // Data state
  invoices: InvoiceWithRelations[];
  invoiceStats: InvoiceStats;

  // Dialog state
  isNewInvoiceDrawerOpen: boolean;
  isViewInvoiceModalOpen: boolean;
  isEditInvoiceModalOpen: boolean;
  isPaymentLedgerDialogOpen: boolean;
  selectedInvoice: InvoiceWithRelations | null;

  // Filter state
  columnFilters: ColumnFilter[];

  // Checkbox state
  selectedItems: Set<string>;

  // Bulk task creation state
  isBulkTaskDialogOpen: boolean;

  // Invoice action loading states
  markingAsPaid: boolean;
  updatingRate: boolean;
  closingInvoice: boolean;
}

/**
 * Custom hook for invoice management logic
 *
 * Encapsulates all business logic, state management, and side effects
 * for the invoice management feature. Follows React best practices with
 * proper memoization and optimization.
 */
export function useInvoiceManagement() {
  const { user: authUser } = useAppSelector((state) => state.auth);
  const searchParams = useSearchParams();
  const router = useRouter();

  // Initial state
  const [state, setState] = useState<InvoiceManagementState>({
    activeTab: "overview",
    viewMode: "table", // Default to table as requested
    searchTerm: "",
    filterStatus: "all",
    loading: true,
    refreshing: false,
    invoices: [],
    invoiceStats: {
      totalInvoices: 0,
      paidInvoicesCount: 0,
      pendingInvoicesCount: 0,
      overdueInvoicesCount: 0,
      paidAmount: 0,
      pendingAmount: 0,
      overdueAmount: 0,
    },
    isNewInvoiceDrawerOpen: false,
    isViewInvoiceModalOpen: false,
    isEditInvoiceModalOpen: false,
    isPaymentLedgerDialogOpen: false,
    selectedInvoice: null,
    columnFilters: [],
    selectedItems: new Set<string>(),
    isBulkTaskDialogOpen: false,

    // Pagination state
    currentPage: 1,

    // Invoice action loading states
    markingAsPaid: false,
    updatingRate: false,
    closingInvoice: false,
  });

  // Fetch invoice data
  const fetchInvoiceData = useCallback(
    async (refresh = false) => {
      if (!authUser?.accountId) return;

      try {
        if (refresh) setState((prev) => ({ ...prev, refreshing: true }));
        else setState((prev) => ({ ...prev, loading: true }));

        const response = await invoiceService.getAllInvoicesWithRelations({
          limit: 100, // Get more invoices for better filtering
          offset: 0,
        });

        if (response.success && response.data) {
          let invoices = response.data;

          // Check for overdue invoices and update them
          const overdueUpdated = await updateOverdueInvoices(invoices);

          // If overdue invoices were updated, fetch fresh data
          if (overdueUpdated) {
            const refreshedResponse =
              await invoiceService.getAllInvoicesWithRelations({
                limit: 100,
                offset: 0,
              });
            if (refreshedResponse.success && refreshedResponse.data) {
              invoices = refreshedResponse.data;
            }
          }

          // Calculate statistics
          const stats = calculateInvoiceStats(invoices);

          setState((prev) => ({
            ...prev,
            invoices,
            invoiceStats: stats,
            loading: false,
          }));
        } else {
          console.error("Failed to fetch invoices:", response.error);
          setState((prev) => ({ ...prev, loading: false }));
        }
      } catch (error) {
        console.error("Error fetching invoices:", error);
        setState((prev) => ({ ...prev, loading: false, refreshing: false }));
      }
    },
    [authUser?.accountId]
  );

  // Handle URL parameter for viewing specific invoice
  const handleUrlInvoiceView = useCallback(async () => {
    const invoiceId = searchParams.get("id");

    if (invoiceId && state.invoices.length > 0) {
      // Find the invoice in the current state
      const invoice = state.invoices.find((inv) => inv.id === invoiceId);

      if (invoice) {
        // Open the view invoice modal with the found invoice
        setState((prev) => ({
          ...prev,
          selectedInvoice: invoice,
          isViewInvoiceModalOpen: true,
        }));

        // Remove the id parameter from URL to clean it up
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete("id");
        const newUrl = newSearchParams.toString()
          ? `${window.location.pathname}?${newSearchParams.toString()}`
          : window.location.pathname;
        router.replace(newUrl);
      } else {
        // Invoice not found in current state, try to fetch it directly
        try {
          const result = await invoiceService.getInvoiceWithCustomer(invoiceId);
          if (result.success && result.data) {
            setState((prev) => ({
              ...prev,
              selectedInvoice: result.data!,
              isViewInvoiceModalOpen: true,
            }));

            // Remove the id parameter from URL
            const newSearchParams = new URLSearchParams(searchParams);
            newSearchParams.delete("id");
            const newUrl = newSearchParams.toString()
              ? `${window.location.pathname}?${newSearchParams.toString()}`
              : window.location.pathname;
            router.replace(newUrl);
          } else {
            // Remove invalid id parameter from URL
            const newSearchParams = new URLSearchParams(searchParams);
            newSearchParams.delete("id");
            const newUrl = newSearchParams.toString()
              ? `${window.location.pathname}?${newSearchParams.toString()}`
              : window.location.pathname;
            router.replace(newUrl);
          }
        } catch (error) {
          console.error("Error fetching invoice from URL parameter:", error);
          // Remove invalid id parameter from URL
          const newSearchParams = new URLSearchParams(searchParams);
          newSearchParams.delete("id");
          const newUrl = newSearchParams.toString()
            ? `${window.location.pathname}?${newSearchParams.toString()}`
            : window.location.pathname;
          router.replace(newUrl);
        }
      }
    }
  }, [searchParams, state.invoices, router]);

  // Auto-update overdue invoices
  const updateOverdueInvoices = useCallback(
    async (invoices: InvoiceWithRelations[]) => {
      const today = new Date();
      const overdueInvoices = invoices.filter((invoice) => {
        if (!invoice.due_at) return false;
        const dueDate = new Date(invoice.due_at);
        return (
          dueDate < today &&
          invoice.status !== "PAID" &&
          invoice.status !== "CLOSED" &&
          invoice.status !== "OVERDUE"
        );
      });

      if (overdueInvoices.length > 0) {
        console.log(
          `Found ${overdueInvoices.length} invoices to mark as overdue`
        );

        try {
          // Update each overdue invoice's status
          const updatePromises = overdueInvoices.map((invoice) =>
            invoiceService.update(invoice.id, { status: "OVERDUE" })
          );

          const updateResults = await Promise.all(updatePromises);
          const successfulUpdates = updateResults.filter(
            (result) => result.success
          ).length;

          if (successfulUpdates > 0) {
            console.log(
              `Successfully marked ${successfulUpdates} invoice(s) as overdue`
            );
            return true; // Indicates that updates were made
          }
        } catch (error) {
          console.error("Error updating overdue invoices:", error);
        }
      }

      return false; // No updates were made
    },
    []
  );

  // Calculate invoice statistics
  const calculateInvoiceStats = useCallback(
    (invoices: InvoiceWithRelations[]): InvoiceStats => {
      const totalInvoices = invoices.length;
      const paidInvoices = invoices.filter((inv) => inv.status === "PAID");
      const pendingInvoices = invoices.filter(
        (inv) => inv.status === "PENDING" || inv.status === "DRAFT"
      );
      const overdueInvoices = invoices.filter((inv) => {
        // Include invoices with OVERDUE status or those that are past due
        if (inv.status === "OVERDUE") return true;
        if (!inv.due_at) return false;
        const dueDate = new Date(inv.due_at);
        const today = new Date();
        return (
          dueDate < today && inv.status !== "PAID" && inv.status !== "CLOSED"
        );
      });

      const paidAmount = paidInvoices.reduce(
        (sum, inv) => sum + (inv.total || 0),
        0
      );
      const pendingAmount = pendingInvoices.reduce(
        (sum, inv) => sum + (inv.total || 0),
        0
      );
      const overdueAmount = overdueInvoices.reduce(
        (sum, inv) => sum + (inv.total || 0),
        0
      );

      return {
        totalInvoices,
        paidInvoicesCount: paidInvoices.length,
        pendingInvoicesCount: pendingInvoices.length,
        overdueInvoicesCount: overdueInvoices.length,
        paidAmount,
        pendingAmount,
        overdueAmount,
      };
    },
    []
  );

  // Return all invoices (filtering moved to component level like cargo management)
  const filteredInvoices = state.invoices;

  // Load data on mount
  useEffect(() => {
    fetchInvoiceData();
  }, [fetchInvoiceData]);

  // Handle URL parameter for invoice viewing
  useEffect(() => {
    handleUrlInvoiceView();
  }, [handleUrlInvoiceView]);

  // Event handlers
  const handleRefresh = useCallback(() => {
    fetchInvoiceData(true);
  }, [fetchInvoiceData]);

  const handleDialogClose = useCallback((dialogType: string) => {
    setState((prev) => ({
      ...prev,
      [`is${dialogType}Open`]: false,
      selectedInvoice: null,
    }));
  }, []);

  const handleInvoiceMutation = useCallback(() => {
    fetchInvoiceData(true);
  }, [fetchInvoiceData]);

  // Bulk actions handlers
  const handleBulkStatusUpdate = useCallback(
    async (status: string) => {
      const selectedIds = Array.from(state.selectedItems);
      if (selectedIds.length === 0) {
        console.warn("No invoices selected for bulk status update");
        return;
      }

      try {
        // Update each selected invoice's status
        const updatePromises = selectedIds.map((invoiceId) =>
          invoiceService.update(invoiceId, { status: status as any })
        );

        const updateResults = await Promise.all(updatePromises);
        const successfulUpdates = updateResults.filter(
          (result) => result.success
        ).length;
        const failedUpdates = updateResults.length - successfulUpdates;

        if (successfulUpdates > 0) {
          console.log(
            `Successfully updated ${successfulUpdates} invoice(s) to ${status}`,
            failedUpdates > 0 ? `${failedUpdates} update(s) failed` : ""
          );

          // Update state directly for successful updates
          setState((prev) => ({
            ...prev,
            selectedItems: new Set(), // Clear selection after successful action
            invoices: prev.invoices.map((invoice) =>
              selectedIds.includes(invoice.id) &&
              updateResults.find(
                (result) => result.success && result.data?.id === invoice.id
              )
                ? { ...invoice, status: status as any }
                : invoice
            ),
          }));
        } else {
          console.error("Failed to update any invoice status");
        }
      } catch (error) {
        console.error("Error in bulk status update:", error);
      }
    },
    [state.selectedItems, fetchInvoiceData]
  );

  const handleBulkDelete = useCallback(() => {
    const selectedIds = Array.from(state.selectedItems);
    if (selectedIds.length === 0) return;

    console.log(`Deleting ${selectedIds.length} invoices:`, selectedIds);
    // TODO: Implement bulk delete logic
    setState((prev) => ({ ...prev, selectedItems: new Set() })); // Clear selection after action
  }, [state.selectedItems]);

  // Clear selections handler
  const handleClearSelections = useCallback(() => {
    setState((prev) => ({ ...prev, selectedItems: new Set() }));
    toast.success("Selections cleared");
  }, []);

  // Handle marking invoice as paid
  const handleMarkAsPaid = useCallback(
    async (
      invoice: InvoiceWithRelations,
      onPaymentLedgerRequired?: (invoice: InvoiceWithRelations) => void,
      onStatusChanged?: () => void,
      onInvoiceUpdated?: () => void,
      onClose?: () => void
    ) => {
      if (!invoice?.id) {
        alert("Invoice ID is required");
        return;
      }

      try {
        setState((prev) => ({ ...prev, markingAsPaid: true }));

        const result = await invoiceService.updateInvoice(invoice.id, {
          status: "PAID",
        });

        if (result.success) {
          // Check for validation errors in the error field
          if (result.error && result.error.startsWith("VALIDATION_ERROR:")) {
            const validationMessage = result.error.replace(
              "VALIDATION_ERROR: ",
              ""
            );
            alert(`⚠️ Payment Validation Failed\n\n${validationMessage}`);
            return;
          }

          console.log("✅ Invoice marked as paid successfully:", result.data);

          // Check if this is a NORMAL type invoice that requires ledger follow-up
          const invoiceType = invoice.type?.toUpperCase() || "NORMAL";
          if (invoiceType === "NORMAL" && onPaymentLedgerRequired) {
            console.log("🏦 NORMAL invoice paid - triggering ledger dialog");
            onPaymentLedgerRequired(invoice);
          }

          // Prioritize status change callback for immediate stats update
          if (onStatusChanged) {
            console.log("🚀 Triggering optimized status change callback");
            onStatusChanged();
          } else if (onInvoiceUpdated) {
            // Fallback to full refresh if status callback not provided
            console.log("🔄 Fallback to full refresh callback");
            onInvoiceUpdated();
          } else {
            // Direct state mutation as final fallback
            console.log("🔄 Using direct state mutation for invoice update");
            setState((prev) => ({
              ...prev,
              invoices: prev.invoices.map((inv) =>
                inv.id === invoice.id ? { ...inv, status: "PAID" } : inv
              ),
            }));
          }

          // Close the modal
          if (onClose) {
            onClose();
          }
        } else {
          console.error("Failed to mark invoice as paid:", result.error);
          alert("Failed to mark invoice as paid. Please try again.");
        }
      } catch (error: any) {
        console.error("Error marking invoice as paid:", error);
        alert(
          "An error occurred while marking the invoice as paid. Please try again."
        );
      } finally {
        setState((prev) => ({ ...prev, markingAsPaid: false }));
      }
    },
    []
  );

  // Handle updating custom conversion rate
  const handleUpdateConversionRate = useCallback(
    async (
      invoice: InvoiceWithRelations,
      customRate: string,
      onInvoiceUpdated?: () => void,
      setIsEditingRate?: (editing: boolean) => void
    ) => {
      if (!invoice?.id) {
        alert("Invoice ID is required");
        return;
      }

      const rateValue = parseFloat(customRate);
      if (isNaN(rateValue) || rateValue <= 0) {
        alert("Please enter a valid conversion rate");
        return;
      }

      try {
        setState((prev) => ({ ...prev, updatingRate: true }));

        const result = await invoiceService.updateInvoice(invoice.id, {
          currency_conv_rate: rateValue,
        });

        if (result.success) {
          console.log("Conversion rate updated successfully:", result.data);

          // Show success notification
          const { toast } = await import("sonner");
          toast.success("Conversion rate updated successfully!", {
            description: `New rate: 1 USD = ${rateValue.toLocaleString()} TZS`,
          });

          // Call the callback to refresh the parent component
          if (onInvoiceUpdated) {
            onInvoiceUpdated();
          } else {
            // Direct state mutation as fallback
            setState((prev) => ({
              ...prev,
              invoices: prev.invoices.map((inv) =>
                inv.id === invoice.id
                  ? { ...inv, currency_conv_rate: rateValue }
                  : inv
              ),
            }));
          }

          if (setIsEditingRate) {
            setIsEditingRate(false);
          }
        } else {
          console.error("Failed to update conversion rate:", result.error);
          const { toast } = await import("sonner");
          toast.error("Failed to update conversion rate", {
            description: result.error || "Please try again.",
          });
        }
      } catch (error: any) {
        console.error("Error updating conversion rate:", error);
        const { toast } = await import("sonner");
        toast.error("Error updating conversion rate", {
          description: "An unexpected error occurred. Please try again.",
        });
      } finally {
        setState((prev) => ({ ...prev, updatingRate: false }));
      }
    },
    []
  );

  // Download invoice as PDF function
  const handleDownloadInvoice = async (invoice: InvoiceWithRelations) => {
    try {
      // Get the full invoice with customer data
      const invoiceResult = await invoiceService.getInvoiceWithCustomer(
        invoice.id
      );

      if (!invoiceResult.success || !invoiceResult.data) {
        alert("Failed to fetch invoice data for download");
        return;
      }

      const invoiceWithCustomer = invoiceResult.data;

      // Prepare template data
      const templateData = prepareInvoiceTemplateData(invoiceWithCustomer);

      // Generate PDF using the new template function
      const pdfBlob = await generateInvoiceTemplate(templateData);

      // Generate filename with customer name or supplier tracking number as prefix
      const invoiceNumber =
        templateData.invoice.inv_number || templateData.invoice.id;

      // Get customer name or supplier tracking number for prefix
      const customerName = templateData.customer?.name;
      const supplierTrackingNumber = (invoiceWithCustomer as any).supplier
        ?.tracking_number;
      const prefix = supplierTrackingNumber || customerName;

      // Build filename: invoice-{invoice_number}-{customer_name/supplier_tracking}.pdf
      const fileName = prefix
        ? `invoice-${invoiceNumber}-${prefix.replace(/[^a-zA-Z0-9-_]/g, "_")}.pdf`
        : `invoice-${invoiceNumber}.pdf`;

      // Download the PDF
      downloadPDF(pdfBlob, fileName);

      // Optionally, save the document to the database
      try {
        await documentService.generateAndStoreDocument(
          {
            name: `Invoice ${templateData.invoice.inv_number}`,
            category: "invoices",
            description: `Generated invoice document for ${templateData.customer?.name || "customer"}`,
            associatedTable: "invoices",
            associatedId: templateData.invoice.id,
            details: {
              invoiceNumber: templateData.invoice.inv_number,
              customerId: templateData.customer?.id,
              totalAmount: templateData.totals.total,
            },
            content: pdfBlob,
            contentType: "application/pdf",
          },
          "system" // You might want to get the actual user ID here
        );
      } catch (docError) {
        console.warn("Failed to save document to database:", docError);
        // Don't fail the download if document saving fails
      }
    } catch (error) {
      console.error("Error downloading invoice:", error);
      alert("Failed to download invoice. Please try again.");
    }
  };

  // Handle sharing invoice with customer
  const handleShareInvoice = async (invoice: InvoiceWithRelations) => {
    if (!invoice?.id) {
      alert("Invoice ID is required");
      return;
    }

    if (!invoice.customer?.phone) {
      alert("Customer phone is required to share invoice");
      return;
    }

    try {
      console.log("Starting invoice sharing for:", invoice.id);

      // Get the full invoice with customer data
      const invoiceResult = await invoiceService.getInvoiceWithCustomer(
        invoice.id
      );

      if (!invoiceResult.success || !invoiceResult.data) {
        alert("Failed to fetch invoice data for sharing");
        return;
      }

      const invoiceWithCustomer = invoiceResult.data;

      // Transform customer data to contact format
      const customerContact = await transformCustomerToContact(
        invoiceWithCustomer.customer as any,
        "both" // Use both email and SMS/WhatsApp if available
      );

      // Prepare template data and generate PDF
      const templateData = prepareInvoiceTemplateData(invoiceWithCustomer);
      const pdfBlob = await generateInvoiceTemplate(templateData);

      // Generate filename with customer name prefix
      const invoiceNumber =
        templateData.invoice.inv_number || templateData.invoice.id;
      const customerName = templateData.customer?.name;
      const fileName = customerName
        ? `${customerName.replace(/[^a-zA-Z0-9-_]/g, "_")}-invoice-${invoiceNumber}.pdf`
        : `invoice-${invoiceNumber}.pdf`;

      // Save PDF to storage and get signed URL
      const documentResult = await documentService.generateAndStoreDocument(
        {
          name: `Invoice ${invoiceNumber}`,
          category: "invoices",
          description: `Generated invoice document for ${customerName || "customer"}`,
          associatedTable: "invoices",
          associatedId: templateData.invoice.id,
          details: {
            invoiceNumber: invoiceNumber,
            customerId: templateData.customer?.id,
            totalAmount: templateData.totals.total,
          },
          content: pdfBlob,
          contentType: "application/pdf",
        },
        authUser?.accountId || "system"
      );

      if (!documentResult.success || !documentResult.data) {
        alert("Failed to save invoice document for sharing");
        return;
      }

      // Get signed URL for email attachment
      const signedUrlResult = await documentService.getDocumentDownloadUrl(
        documentResult.data.path,
        3600 // 1 hour expiry
      );

      if (!signedUrlResult.success || !signedUrlResult.data) {
        alert("Failed to generate download URL for invoice");
        return;
      }

      // Send invoice alert with PDF attachment path
      const result = await CustomerAlertsService.sendInvoiceAlert(
        customerContact,
        {
          invoiceNumber: invoiceNumber,
          totalAmount: templateData.totals.total || 0,
          currency: "USD", // Can be enhanced with invoice currency
          dueDate: templateData.invoice.due_at || undefined,
          status: templateData.invoice.status || "PENDING",
          trackingNumber: templateData.invoice.id,
        },
        {
          path: signedUrlResult.data,
          fileName: fileName,
          // No content - will be downloaded from the signed URL
        },
        {
          urgency: "medium",
          documentUrl: `${window.location.origin}/invoice-workflow/${invoice.id}`,
          customMessage:
            "Please review the attached invoice and make payment by the due date.",
        }
      );

      if (result.success) {
        // Update shared status in the database and local state
        const updateResult = await updateSharedStatus(invoice.id, true);

        if (updateResult.success) {
          alert(`Invoice shared successfully with ${customerContact.name}!`);
          console.log("Invoice shared successfully:", result);
        } else {
          console.warn(
            "Invoice shared but failed to update shared status:",
            updateResult.error
          );
          alert(
            `Invoice shared with ${customerContact.name}, but failed to update status.`
          );
        }
      } else {
        alert(
          `Failed to share invoice: ${result.errors?.join(", ") || "Unknown error"}`
        );
        console.error("Failed to share invoice:", result.errors);
      }
    } catch (error) {
      console.error("Error sharing invoice:", error);
      alert("Failed to share invoice. Please try again.");
    }
  };

  // Handle closing invoice with remaining amount as credit
  const handleCloseInvoice = useCallback(
    async (
      invoice: InvoiceWithRelations,
      onStatusChanged?: () => void,
      onClose?: () => void
    ) => {
      if (!invoice?.id) {
        alert("Invoice ID is required");
        return;
      }

      if (!authUser?.accountId) {
        alert("User account information is required");
        return;
      }

      const remainingAmount = invoice.total || 0;
      if (remainingAmount <= 0) {
        alert("Invoice has no remaining balance to close as credit");
        return;
      }

      const confirmMessage = `Close this invoice and record the remaining amount of $${remainingAmount.toLocaleString()} as credit?\n\nThis action cannot be undone.`;
      if (!confirm(confirmMessage)) {
        return;
      }

      try {
        setState((prev) => ({ ...prev, closingInvoice: true }));

        // Step 1: Extract cargo IDs from line items
        const lineItems = invoice.line_items || [];
        const cargoIds = lineItems
          .map((item: any) => item.cargo_id)
          .filter((id: string) => id && id.trim() !== "");

        console.log("📦 Found cargo IDs in invoice line items:", cargoIds);

        // Step 2: Get batch IDs from cargo data
        const batchIds = new Set<string>();
        const batchLedgerMap = new Map<string, any>();

        if (cargoIds.length > 0) {
          for (const cargoId of cargoIds) {
            try {
              const cargoResult =
                await cargoService.getCargoWithRelations(cargoId);
              if (cargoResult.success && cargoResult.data?.batch_id) {
                batchIds.add(cargoResult.data.batch_id);
                console.log(
                  `📦 Cargo ${cargoId} belongs to batch ${cargoResult.data.batch_id}`
                );
              }
            } catch (error) {
              console.warn(`Failed to get cargo ${cargoId}:`, error);
            }
          }
        }

        console.log("📋 Unique batch IDs found:", Array.from(batchIds));

        // Step 3: Find batch ledgers for each unique batch
        for (const batchId of batchIds) {
          try {
            const ledgersResult = await ledgerService.getAllLedgers({
              filters: {
                associated_table: "batches",
                associated_id: batchId,
              },
              limit: 1,
            });

            if (
              ledgersResult.success &&
              ledgersResult.data &&
              ledgersResult.data.length > 0
            ) {
              batchLedgerMap.set(batchId, ledgersResult.data[0]);
              console.log(
                `🏦 Found ledger for batch ${batchId}:`,
                ledgersResult.data[0]?.id
              );
            } else {
              console.warn(`⚠️ No ledger found for batch ${batchId}`);
            }
          } catch (error) {
            console.warn(`Failed to get ledger for batch ${batchId}:`, error);
          }
        }

        // Step 4: Record transactions on batch ledgers or fallback to "Realised Invoices" ledger
        let transactionsCreated = 0;

        if (batchLedgerMap.size > 0) {
          // Create transactions on batch ledgers
          const amountPerBatch = remainingAmount / batchLedgerMap.size;

          for (const [, ledger] of batchLedgerMap) {
            try {
              const transactionResult =
                await transactionService.createTransaction({
                  name: `Invoice ${invoice.inv_number || invoice.id} Closure Credit`,
                  status: "ACTIVE" as any,
                  tags: ["invoice-closure", "credit", "batch"],
                  context: `Closed invoice ${invoice.inv_number || invoice.id} - recorded remaining amount as credit`,
                  type: "CREDIT",
                  amount: amountPerBatch,
                  book: "expenses",
                  ledger_id: ledger.id,
                  account_id: authUser.accountId,
                  associated_table: "invoices",
                  associated_id: invoice.id,
                });

              if (transactionResult.success) {
                transactionsCreated++;
                console.log(
                  `✅ Created transaction on batch ledger ${ledger.id}`
                );
              } else {
                console.error(
                  `❌ Failed to create transaction on batch ledger ${ledger.id}:`,
                  transactionResult.error
                );
              }
            } catch (error) {
              console.error(
                `Error creating transaction on batch ledger ${ledger.id}:`,
                error
              );
            }
          }
        }

        // Step 5: If no batch ledgers exist, use "Realised Invoices" ledger
        if (batchLedgerMap.size === 0) {
          console.log(
            "🏦 No batch ledgers found, using Realised Invoices ledger"
          );

          try {
            const realisedLedgerResult =
              await ledgerService.findOrCreateRealisedInvoicesLedger(
                authUser.accountId
              );

            if (realisedLedgerResult.success && realisedLedgerResult.data) {
              const realisedLedger = realisedLedgerResult.data;

              const transactionResult =
                await transactionService.createTransaction({
                  name: `Invoice ${invoice.inv_number || invoice.id} Closure Credit`,
                  status: "ACTIVE" as any,
                  tags: ["invoice-closure", "credit", "realised"],
                  context: `Closed invoice ${invoice.inv_number || invoice.id} - no batch ledgers available, recorded on Realised Invoices ledger`,
                  type: "CREDIT",
                  amount: remainingAmount,
                  book: "expenses",
                  ledger_id: realisedLedger.id,
                  account_id: authUser.accountId,
                  associated_table: "invoices",
                  associated_id: invoice.id,
                });

              if (transactionResult.success) {
                transactionsCreated++;
                console.log(
                  "✅ Created transaction on Realised Invoices ledger"
                );
              } else {
                console.error(
                  "❌ Failed to create transaction on Realised Invoices ledger:",
                  transactionResult.error
                );
              }
            } else {
              console.error(
                "❌ Failed to find or create Realised Invoices ledger:",
                realisedLedgerResult.error
              );
            }
          } catch (error) {
            console.error("Error with Realised Invoices ledger:", error);
          }
        }

        // Step 6: Update invoice status to CLOSED
        const result = await invoiceService.updateInvoice(invoice.id, {
          status: "CLOSED",
          total: 0, // Set total to 0 as the remaining amount is recorded as credit
        });

        if (result.success) {
          console.log("✅ Invoice closed successfully:", result.data);

          // Call the callback to refresh the parent component
          if (onStatusChanged) {
            onStatusChanged();
          } else {
            // Direct state mutation as fallback
            setState((prev) => ({
              ...prev,
              invoices: prev.invoices.map((inv) =>
                inv.id === invoice.id ? { ...inv, status: "CLOSED" } : inv
              ),
            }));
          }

          // Close the modal
          if (onClose) {
            onClose();
          }

          const successMessage =
            transactionsCreated > 0
              ? `Invoice closed successfully. Remaining amount of $${remainingAmount.toLocaleString()} recorded as credit across ${transactionsCreated} transaction(s).`
              : `Invoice closed successfully, but no transactions were recorded due to errors.`;

          alert(successMessage);
        } else {
          console.error("Failed to close invoice:", result.error);
          alert("Failed to close invoice. Please try again.");
        }
      } catch (error: any) {
        console.error("Error closing invoice:", error);
        alert("An error occurred while closing the invoice. Please try again.");
      } finally {
        setState((prev) => ({ ...prev, closingInvoice: false }));
      }
    },
    [authUser]
  );

  // State update handlers (like cargo management)
  const updateState = useCallback(
    (updates: Partial<InvoiceManagementState>) => {
      setState((prev) => ({ ...prev, ...updates }));
    },
    []
  );

  // Update shared status function
  const updateSharedStatus = useCallback(
    async (invoiceId: string, shared: boolean = true) => {
      try {
        const result = await invoiceService.update(invoiceId, {
          shared,
        });

        if (result.success) {
          // Update the invoice in state
          setState((prev) => ({
            ...prev,
            invoices: prev.invoices.map((invoice) =>
              invoice.id === invoiceId ? { ...invoice, shared } : invoice
            ),
          }));
          return { success: true, error: null };
        } else {
          return {
            success: false,
            error: result.error || "Failed to update shared status",
          };
        }
      } catch (error) {
        console.error("Error updating shared status:", error);
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to update shared status",
        };
      }
    },
    []
  );

  const handleInvoiceAction = useCallback(
    (action: string, invoice?: InvoiceWithRelations) => {
      switch (action) {
        case "create":
          setState((prev) => ({ ...prev, isNewInvoiceDrawerOpen: true }));
          break;
        case "view":
          setState((prev) => ({
            ...prev,
            selectedInvoice: invoice!,
            isViewInvoiceModalOpen: true,
          }));
          break;
        case "edit":
          setState((prev) => ({
            ...prev,
            selectedInvoice: invoice!,
            isEditInvoiceModalOpen: true,
          }));
          break;
        case "download":
          if (invoice) {
            handleDownloadInvoice(invoice);
          }
          break;
        case "share":
          if (invoice) {
            handleShareInvoice(invoice);
          }
          break;
        case "payment":
          setState((prev) => ({
            ...prev,
            selectedInvoice: invoice!,
            isPaymentLedgerDialogOpen: true,
          }));
          break;
      }
    },
    [handleDownloadInvoice, handleShareInvoice]
  );

  // Bulk task creation handlers
  const handleBulkCreateTasks = useCallback(() => {
    if (state.selectedItems.size === 0) {
      toast.error("Please select invoices to create tasks for");
      return;
    }
    setState((prev) => ({ ...prev, isBulkTaskDialogOpen: true }));
  }, [state.selectedItems.size]);

  const setIsBulkTaskDialogOpen = useCallback((isOpen: boolean) => {
    setState((prev) => ({ ...prev, isBulkTaskDialogOpen: isOpen }));
  }, []);

  const selectedItemsForTasks = Array.from(state.selectedItems).map(
    (invoiceId) => {
      const invoice = state.invoices.find((i) => i.id === invoiceId);
      return {
        id: invoiceId,
        name: invoice?.inv_number || "Unknown Invoice",
        identifier: invoice?.inv_number,
      };
    }
  );

  const handleTasksCreated = useCallback(() => {
    // Clear selections and refresh data
    setState((prev) => ({ ...prev, selectedItems: new Set() }));
    fetchInvoiceData(true);
  }, [fetchInvoiceData]);

  // Reset pagination when filters change (like cargo management)
  useEffect(() => {
    setState((prev) => ({ ...prev, currentPage: 1 }));
  }, [state.searchTerm, state.filterStatus, state.columnFilters]);

  return {
    state,
    filteredInvoices,
    updateState,
    handleRefresh,
    handleInvoiceAction,
    handleDialogClose,
    handleInvoiceMutation,
    handleBulkStatusUpdate,
    handleBulkDelete,
    handleClearSelections,
    handleDownloadInvoice,
    handleShareInvoice,
    updateSharedStatus,
    // Invoice action handlers
    handleMarkAsPaid,
    handleUpdateConversionRate,
    handleCloseInvoice,
    // Bulk task creation
    handleBulkCreateTasks,
    isBulkTaskDialogOpen: state.isBulkTaskDialogOpen,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    handleTasksCreated,
    // URL parameter handling
    handleUrlInvoiceView,
  };
}
