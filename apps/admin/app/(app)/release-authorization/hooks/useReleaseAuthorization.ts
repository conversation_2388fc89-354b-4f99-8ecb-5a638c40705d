"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { redirect } from "next/navigation";
import { useAppSelector } from "@/store/hooks";
import {
  releaseAuthorizationService,
  type ShipmentForRelease,
} from "@/lib/logistics";
import { codeGeneratorService } from "@/lib/logistics/operations/code-generator";
import {
  CustomerAlertsService,
  transformCustomerToContact,
} from "@/lib/customerAlerts";
import { generateReleaseDocument } from "@/lib/release-document-generator";
import { documentService } from "@/lib/logistics/operations/documents";
import type { PersonnelInfo } from "@/lib/types/invoice-template";
import { toast } from "sonner";
import {
  type ReleaseAuthorizationState,
  type ReleaseAuthorizationDisplay,
  type ReleaseStats,
} from "../types";
import { transformShipmentToDisplay } from "../utils";

/**
 * Custom hook for release authorization state and business logic
 *
 * This hook encapsulates all the state management and business logic
 * for the release authorization page, following React best practices.
 */
export function useReleaseAuthorization() {
  // Get authenticated user
  const { user: authUser } = useAppSelector((state) => state.auth);

  // State management
  const [state, setState] = useState<ReleaseAuthorizationState>({
    viewMode: "table",
    searchTerm: "",
    statusFilter: "all",
    currentPage: 1,
    loading: true,
    refreshing: false,
    error: null,
    shipments: [],
    releaseStats: {
      totalCargo: 0,
      pendingAuthorization: 0,
      authorized: 0,
      onHold: 0,
    },
    isAuthModalOpen: false,
    selectedShipment: null,
    columnFilters: [],
    selectedShipments: new Set(),
    bulkActionLoading: false,
    isBulkTaskDialogOpen: false,
  });

  // Update state helper
  const updateState = useCallback(
    (updates: Partial<ReleaseAuthorizationState>) => {
      setState((prev) => ({ ...prev, ...updates }));
    },
    []
  );

  // Fetch release authorization data
  const fetchReleaseData = useCallback(
    async (refresh = false) => {
      if (!authUser) return;

      try {
        if (refresh) setState((prev) => ({ ...prev, refreshing: true }));
        else setState((prev) => ({ ...prev, loading: true }));

        // Fetch shipments for release
        const shipmentsResult =
          await releaseAuthorizationService.getShipmentsForRelease({
            limit: 100,
            page: 1,
          });

        if (shipmentsResult.success && shipmentsResult.data) {
          // Transform shipments to display format
          const transformedShipments = shipmentsResult.data.map(
            (shipment: ShipmentForRelease) =>
              transformShipmentToDisplay(shipment)
          );

          setState((prev) => ({
            ...prev,
            shipments: transformedShipments,
            error: null,
          }));
        } else {
          setState((prev) => ({
            ...prev,
            error: shipmentsResult.error || "Failed to fetch shipments",
          }));
        }

        // Fetch statistics
        const statsResult =
          await releaseAuthorizationService.getReleaseAuthorizationStats();
        if (statsResult.success && statsResult.data) {
          setState((prev) => ({
            ...prev,
            releaseStats: statsResult.data,
          }));
        }
      } catch (error) {
        console.error("Error fetching release authorization data:", error);
        setState((prev) => ({
          ...prev,
          error:
            error instanceof Error ? error.message : "Failed to fetch data",
        }));
      } finally {
        setState((prev) => ({ ...prev, loading: false, refreshing: false }));
      }
    },
    [authUser]
  );

  // Filter shipments based on search and status
  const filteredShipments = useMemo(() => {
    return state.shipments.filter((shipment) => {
      const matchesSearch =
        !state.searchTerm ||
        shipment.trackingNumber
          ?.toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        shipment.customer
          ?.toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        shipment.releaseCode
          ?.toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        shipment.batchCode
          ?.toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        shipment.origin
          ?.toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        shipment.destination
          ?.toLowerCase()
          .includes(state.searchTerm.toLowerCase());

      const matchesStatus =
        state.statusFilter === "all" || shipment.status === state.statusFilter;

      // Apply column filters
      const matchesColumnFilters = state.columnFilters.every((filter) => {
        const shipmentValue = shipment[filter.column as keyof typeof shipment];
        if (shipmentValue == null) return false;
        return String(shipmentValue)
          .toLowerCase()
          .includes(filter.value.toLowerCase());
      });

      return matchesSearch && matchesStatus && matchesColumnFilters;
    });
  }, [
    state.shipments,
    state.searchTerm,
    state.statusFilter,
    state.columnFilters,
  ]);

  // Initial data fetch
  useEffect(() => {
    if (authUser) {
      fetchReleaseData();
    }
  }, [authUser, fetchReleaseData]);

  // Refresh handler
  const handleRefresh = useCallback(() => {
    fetchReleaseData(true);
  }, [fetchReleaseData]);

  // Handle sharing release document with customer
  const handleShareReleaseDocument = useCallback(
    async (shipment: ReleaseAuthorizationDisplay) => {
      if (!shipment.id) {
        alert("Shipment ID is required");
        return;
      }

      if (!shipment.releaseCode) {
        alert("Release code is required to share document");
        return;
      }

      try {
        console.log("Starting release document sharing for:", shipment.id);

        // Get the full shipment data with customer information
        const shipmentResult =
          await releaseAuthorizationService.getShipmentForRelease(shipment.id);

        if (!shipmentResult.success || !shipmentResult.data) {
          alert("Failed to fetch shipment data for sharing");
          return;
        }

        const shipmentData = shipmentResult.data;

        // Check if customer information is available
        if (!shipmentData?.customers?.phone) {
          alert("Customer phone is required to share release document");
          return;
        }

        // Transform customer data to contact format
        const customerContact = await transformCustomerToContact(
          shipmentData.customers as any,
          "both" // Use both email and SMS/WhatsApp if available
        );

        // Create personnel info for document generation
        const personnelInfo: PersonnelInfo = {
          authorizedBy: authUser?.name || "System",
          role: authUser?.role?.name || "Administrator",
          department: authUser?.role?.department?.name || "Operations",
          contactInfo: authUser?.email || "<EMAIL>",
          signature: `Authorized by ${authUser?.name || "System"}`,
        };

        // Generate release document PDF
        const documentResult = await generateReleaseDocument(
          shipmentData,
          personnelInfo,
          {
            autoDownload: false, // Don't auto-download, we'll share it
            additionalData: {
              notes:
                "Please bring valid ID for cargo collection. Collection hours: 8AM-5PM, Monday-Friday.",
              specialInstructions:
                "Contact us if you have any questions about the collection process.",
            },
          }
        );

        if (!documentResult.success || !documentResult.data) {
          alert("Failed to generate release document for sharing");
          return;
        }

        // Save PDF to storage and get signed URL
        const storeResult = await documentService.generateAndStoreDocument(
          {
            name: `Release Authorization ${shipment.releaseCode}`,
            category: "release-authorizations",
            description: `Release authorization document for ${shipment.trackingNumber}`,
            associatedTable: "shipments",
            associatedId: shipment.id,
            details: {
              releaseCode: shipment.releaseCode,
              trackingNumber: shipment.trackingNumber,
              customerId: shipmentData.customers?.id,
              documentNumber: documentResult.data.documentNumber,
            },
            content: documentResult.data.pdfBlob,
            contentType: "application/pdf",
          },
          authUser?.accountId || "system"
        );

        if (!storeResult.success || !storeResult.data) {
          alert("Failed to save release document for sharing");
          return;
        }

        // Get signed URL for email attachment
        const signedUrlResult = await documentService.getDocumentDownloadUrl(
          storeResult.data.path,
          3600 // 1 hour expiry
        );

        if (!signedUrlResult.success || !signedUrlResult.data) {
          alert("Failed to generate download URL for release document");
          return;
        }

        // Send release document alert with PDF attachment
        const result = await CustomerAlertsService.sendReleaseDocumentAlert(
          customerContact,
          {
            releaseCode: shipment.releaseCode,
            trackingNumber: shipment.trackingNumber,
            instructions:
              "Please bring valid ID and this release document for cargo collection. Collection hours: 8AM-5PM, Monday-Friday.",
            qrCodeData: `${window.location.origin}/verify/${shipment.releaseCode}`,
          },
          {
            path: signedUrlResult.data,
            fileName: documentResult.data.fileName,
            // No content - will be downloaded from the signed URL
          },
          {
            urgency: "high",
            documentUrl: `${window.location.origin}/release-authorization/${shipment.id}`,
            customMessage:
              "Your cargo is ready for collection. Please review the attached release authorization document.",
          }
        );

        if (result.success) {
          // Update shared status in the database
          const updateResult =
            await releaseAuthorizationService.updateSharedStatus(
              shipment.id,
              true
            );

          if (updateResult.success) {
            // Update local state to reflect shared status
            updateState((prevState) => ({
              shipments: prevState.shipments.map((s) =>
                s.id === shipment.id ? { ...s, shared: true } : s
              ),
            }));

            alert(
              `Release document shared successfully with ${customerContact.name}!`
            );
            console.log("Release document shared successfully:", result);
          } else {
            console.warn(
              "Document shared but failed to update shared status:",
              updateResult.error
            );
            alert(
              `Release document shared with ${customerContact.name}, but failed to update status.`
            );
          }
        } else {
          alert(
            `Failed to share release document: ${result.errors?.join(", ") || "Unknown error"}`
          );
          console.error("Failed to share release document:", result.errors);
        }
      } catch (error) {
        console.error("Error sharing release document:", error);
        alert("Failed to share release document. Please try again.");
      }
    },
    []
  );

  // Authorization action handlers
  const handleAuthorizationAction = useCallback(
    (action: string, shipment?: ReleaseAuthorizationDisplay) => {
      switch (action) {
        case "authorize":
          if (shipment) {
            updateState({
              isAuthModalOpen: true,
              selectedShipment: shipment,
            });
          }
          break;
        case "view":
          if (shipment) {
            redirect(`/release-authorization/${shipment.id}`);
          }
          break;
        case "share":
          if (shipment) {
            handleShareReleaseDocument(shipment);
          }
          break;
        default:
          break;
      }
    },
    [updateState]
  );

  // Dialog close handlers
  const handleDialogClose = useCallback(
    (dialogType: string) => {
      switch (dialogType) {
        case "authorization":
          updateState({
            isAuthModalOpen: false,
            selectedShipment: null,
          });
          break;
        default:
          break;
      }
    },
    [updateState]
  );

  // Mutation handler (for updates)
  const handleMutation = useCallback(() => {
    fetchReleaseData(true);
  }, [fetchReleaseData]);

  // Generate release code
  const generateReleaseCode = useCallback((cargoTrackingNumber: string) => {
    return codeGeneratorService.generateReleaseCode(cargoTrackingNumber);
  }, []);

  // Update shipment in state
  const updateShipmentInState = useCallback(
    (shipmentId: string, updates: Partial<ReleaseAuthorizationDisplay>) => {
      setState((prev) => ({
        ...prev,
        shipments: prev.shipments.map((shipment) =>
          shipment.id === shipmentId ? { ...shipment, ...updates } : shipment
        ),
      }));
    },
    []
  );

  // Update shared status function
  const updateSharedStatus = useCallback(
    async (cargoId: string, shared: boolean = true) => {
      try {
        const result = await releaseAuthorizationService.updateSharedStatus(
          cargoId,
          shared
        );

        if (result.success) {
          // Update the shipment in state
          setState((prev) => ({
            ...prev,
            shipments: prev.shipments.map((shipment) =>
              shipment.id === cargoId ? { ...shipment, shared } : shipment
            ),
          }));
          return { success: true, error: null };
        } else {
          return {
            success: false,
            error: result.error || "Failed to update shared status",
          };
        }
      } catch (error) {
        console.error("Error updating shared status:", error);
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to update shared status",
        };
      }
    },
    []
  );

  // Clear selections handler
  const handleClearSelections = useCallback(() => {
    updateState({ selectedShipments: new Set() });
    toast.success("Selections cleared");
  }, [updateState]);

  // Bulk task creation handlers
  const handleBulkCreateTasks = useCallback(() => {
    if (state.selectedShipments.size === 0) {
      toast.error("Please select shipments to create tasks for");
      return;
    }
    updateState({ isBulkTaskDialogOpen: true });
  }, [state.selectedShipments.size, updateState]);

  const setIsBulkTaskDialogOpen = useCallback(
    (isOpen: boolean) => {
      updateState({ isBulkTaskDialogOpen: isOpen });
    },
    [updateState]
  );

  const selectedItemsForTasks = Array.from(state.selectedShipments).map(
    (shipmentId) => {
      const shipment = state.shipments.find((s) => s.id === shipmentId);
      return {
        id: shipmentId,
        name: shipment?.trackingNumber || "Unknown Shipment",
        identifier: shipment?.trackingNumber,
      };
    }
  );

  const handleTasksCreated = useCallback(() => {
    // Clear selections and refresh data
    updateState({ selectedShipments: new Set() });
    fetchReleaseData(true);
  }, [updateState, fetchReleaseData]);

  return {
    state,
    filteredShipments,
    updateState,
    handleRefresh,
    handleAuthorizationAction,
    handleShareReleaseDocument,
    handleDialogClose,
    handleMutation,
    generateReleaseCode,
    updateShipmentInState,
    updateSharedStatus,
    handleClearSelections,
    handleBulkCreateTasks,
    isBulkTaskDialogOpen: state.isBulkTaskDialogOpen,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    handleTasksCreated,
  };
}
