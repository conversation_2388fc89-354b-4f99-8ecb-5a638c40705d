import { type ShipmentForRelease } from "@/lib/logistics";
import { type ColumnFilter } from "@/components/ui/filter-panel";

// Base release authorization data
interface BaseReleaseData {
  id: string;
  trackingNumber: string;
  customer: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

// Batch information
interface BatchInfo {
  batchCode?: string;
  batchId?: string;
  origin: string;
  destination: string;
  weight?: string;
  container?: string;
}

// Authorization details
interface AuthorizationDetails {
  releaseCode?: string;
  qrVerification: boolean;
  biometricVerification: boolean;
  hasReleaseAuthorization: boolean;
  releaseAuthorization?: any;
  verificationNotes?: string;
}

// Document status
interface DocumentStatus {
  docsComplete: boolean;
  paymentComplete: boolean;
  documentGenerated: boolean;
  shared: boolean;
  documentGeneratedAt?: string;
  documentGeneratedBy?: string;
}

// Agent information
interface AgentInfo {
  agent?: string;
  requestedDate?: string;
  authorizedDate?: string;
  authorizedBy?: string;
  scannedByDeviceId?: string;
}

// Combined display interface
export interface ReleaseAuthorizationDisplay
  extends BaseReleaseData,
    BatchInfo,
    AuthorizationDetails,
    DocumentStatus,
    AgentInfo {
  cargoType: string;
  arrivalDate: string;
}

// Release Authorization Statistics
export interface ReleaseStats {
  totalCargo: number;
  pendingAuthorization: number;
  authorized: number;
  onHold: number;
}

// UI state interfaces
interface ViewState {
  viewMode: "cards" | "table";
  searchTerm: string;
  statusFilter: string;
  currentPage: number;
  loading: boolean;
  refreshing: boolean;
  error: string | null;
}

interface DataState {
  shipments: ReleaseAuthorizationDisplay[];
  releaseStats: ReleaseStats;
}

interface DialogState {
  isAuthModalOpen: boolean;
  selectedShipment: ReleaseAuthorizationDisplay | null;
  isBulkTaskDialogOpen: boolean;
}

interface FilterState {
  columnFilters: ColumnFilter[];
}

interface BulkActionState {
  selectedShipments: Set<string>;
  bulkActionLoading: boolean;
}

// Combined state interface
export interface ReleaseAuthorizationState
  extends ViewState,
    DataState,
    DialogState,
    FilterState,
    BulkActionState {}

export const ITEMS_PER_PAGE = 10;

// Document data interface
export interface DocumentData {
  fileName: string;
  documentNumber: string;
  qrCodeData: string;
  downloadUrl?: string;
  pdfBlob?: Blob;
}

// Authorization modal state
export interface AuthorizationModalState {
  releaseCode: string;
  documentGenerated: boolean;
  generating: boolean;
  error: string | null;
  comments: string;
  loadingExistingData: boolean;
  documentData: DocumentData | null;
}
