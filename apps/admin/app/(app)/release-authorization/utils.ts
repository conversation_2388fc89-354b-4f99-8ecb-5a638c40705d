import { type ShipmentForRelease } from "@/lib/logistics";
import { type ReleaseAuthorizationDisplay } from "./types";

/**
 * Transform shipment data from API to display format
 */
export function transformShipmentToDisplay(
  shipment: ShipmentForRelease
): ReleaseAuthorizationDisplay {
  return {
    id: shipment.id,
    trackingNumber: shipment.trackingNumber || "",
    batchCode: shipment.batchCode,
    batchId: shipment.batchId,
    releaseCode: shipment.releaseCode,
    customer: shipment.customer || "Unknown Customer",
    cargoType: shipment.cargoType || "GENERAL",
    status: shipment.status || "pending",
    qrVerification: shipment.qrVerification || false,
    biometricVerification: shipment.biometricVerification || false,
    arrivalDate: shipment.arrivalDate || new Date().toISOString(),
    origin: shipment.origin || "Unknown Origin",
    destination: shipment.destination || "Unknown Destination",
    weight: shipment.weight,
    container: shipment.container,
    docsComplete: shipment.docsComplete || false,
    paymentComplete: shipment.paymentComplete || false,
    documentGenerated: shipment.documentGenerated || false,
    shared: shipment.shared || false,
    hasReleaseAuthorization: shipment.hasReleaseAuthorization || false,
    releaseAuthorization: shipment.releaseAuthorization,
    agent: shipment.agent,
    requestedDate: shipment.requestedDate,
    authorizedDate: shipment.authorizedDate,
    authorizedBy: shipment.authorizedBy,
    scannedByDeviceId: shipment.scannedByDeviceId,
    documentGeneratedAt: shipment.documentGeneratedAt,
    documentGeneratedBy: shipment.documentGeneratedBy,
    verificationNotes: shipment.verificationNotes,
    createdAt: shipment.createdAt || new Date().toISOString(),
    updatedAt: shipment.updatedAt || new Date().toISOString(),
  };
}

/**
 * Format date for display
 */
export function formatDate(dateString: string): string {
  try {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  } catch {
    return "N/A";
  }
}

/**
 * Format date and time for display
 */
export function formatDateTime(dateString: string): string {
  try {
    return new Date(dateString).toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    return "N/A";
  }
}

/**
 * Get status color class
 */
export function getStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    pending: "bg-amber-100 text-amber-800",
    authorized: "bg-green-100 text-green-800",
    hold: "bg-red-100 text-red-800",
    rejected: "bg-gray-100 text-gray-800",
    verified: "bg-blue-100 text-blue-800",
  };
  return statusColors[status] || "bg-gray-100 text-gray-800";
}

/**
 * Get cargo type icon class
 */
export function getCargoTypeIcon(cargoType: string): string {
  const iconMap: Record<string, string> = {
    AIR: "text-blue-500",
    SEA: "text-blue-600",
    LAND: "text-green-600",
  };
  return iconMap[cargoType] || "text-gray-500";
}

/**
 * Calculate release authorization statistics
 */
export function calculateReleaseStats(
  shipments: ReleaseAuthorizationDisplay[]
) {
  return {
    totalCargo: shipments.length,
    pendingAuthorization: shipments.filter((s) => s.status === "pending")
      .length,
    authorized: shipments.filter((s) => s.status === "authorized").length,
    onHold: shipments.filter((s) => s.status === "hold").length,
  };
}
