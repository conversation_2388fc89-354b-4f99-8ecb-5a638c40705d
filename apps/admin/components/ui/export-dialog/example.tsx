/**
 * Example component demonstrating how to use the ExportDialog
 */

"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Download,
  FileSpreadsheet,
  FileText,
  Users,
  Package,
  DollarSign,
  Ship,
} from "lucide-react";
import { ExportDialog } from ".";
import {
  useInvoiceExportDialog,
  useCargoExportDialog,
  useTransactionExportDialog,
  useShipmentExportDialog,
  useCustomerExportDialog,
  useGenericExportDialog,
} from "./useExportDialog";
import { showToast } from "@/lib/utils";

// Sample data for demonstration
const sampleInvoices = [
  {
    id: "1",
    inv_number: "INV-001",
    customer_name: "<PERSON>",
    total: 1500.0,
    subtotal: 1350.0,
    status: "PAID",
    created_at: "2024-01-15T10:30:00Z",
    due_at: "2024-02-15T10:30:00Z",
    currency: "USD",
  },
  {
    id: "2",
    inv_number: "INV-002",
    customer_name: "Jane Smith",
    total: 2200.0,
    subtotal: 2000.0,
    status: "PENDING",
    created_at: "2024-01-16T14:20:00Z",
    due_at: "2024-02-16T14:20:00Z",
    currency: "USD",
  },
  {
    id: "3",
    inv_number: "INV-003",
    customer_name: "Acme Corp",
    total: 3500.0,
    subtotal: 3200.0,
    status: "OVERDUE",
    created_at: "2024-01-10T09:15:00Z",
    due_at: "2024-02-10T09:15:00Z",
    currency: "USD",
  },
];

const sampleCargo = [
  {
    id: "1",
    tracking_number: "CG001",
    china_tracking_number: "CN123456",
    description: "Electronics - Smartphones",
    customer_name: "John Doe",
    weight_value: 15.5,
    cbm_value: 0.25,
    quantity: 10,
    status: "ACTIVE",
    code: "BCH001",
    created_at: "2024-01-15T08:00:00Z",
  },
  {
    id: "2",
    tracking_number: "CG002",
    china_tracking_number: "CN789012",
    description: "Clothing - Winter Jackets",
    customer_name: "Jane Smith",
    weight_value: 8.2,
    cbm_value: 0.18,
    quantity: 5,
    status: "ACTIVE",
    code: "BCH001",
    created_at: "2024-01-15T09:30:00Z",
  },
];

const sampleTransactions = [
  {
    id: "1",
    type: "CREDIT",
    amount: 1500.0,
    description: "Invoice payment - INV-001",
    created_at: "2024-01-20T10:00:00Z",
    ledger_name: "Customer Payments",
    status: "COMPLETED",
    reference: "PAY-001",
  },
  {
    id: "2",
    type: "DEBIT",
    amount: 500.0,
    description: "Shipping costs",
    created_at: "2024-01-21T15:30:00Z",
    ledger_name: "Operating Expenses",
    status: "COMPLETED",
    reference: "EXP-001",
  },
];

export function ExportDialogExample() {
  const [selectedData, setSelectedData] = useState<
    "invoices" | "cargo" | "transactions"
  >("invoices");

  // Specialized export hooks
  const invoiceExport = useInvoiceExportDialog();
  const cargoExport = useCargoExportDialog();
  const transactionExport = useTransactionExportDialog();
  const shipmentExport = useShipmentExportDialog();
  const customerExport = useCustomerExportDialog();
  const genericExport = useGenericExportDialog();

  const handleExportSuccess = (type: "excel" | "pdf", filename: string) => {
    showToast(`${type.toUpperCase()} export completed: ${filename}`);
  };

  const handleExportError = (error: string) => {
    showToast(`Export failed: ${error}`, "error");
  };

  const getCurrentData = () => {
    switch (selectedData) {
      case "invoices":
        return sampleInvoices;
      case "cargo":
        return sampleCargo;
      case "transactions":
        return sampleTransactions;
      default:
        return [];
    }
  };

  const getCurrentHeaders = () => {
    const data = getCurrentData();
    return data.length > 0 ? Object.keys(data[0]) : [];
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Export Dialog Examples</h1>
          <p className="text-gray-600">
            Demonstration of the export dialog component with different data
            types
          </p>
        </div>
      </div>

      {/* Data Type Selector */}
      <div className="flex gap-2">
        <Button
          variant={selectedData === "invoices" ? "default" : "outline"}
          onClick={() => setSelectedData("invoices")}
          className="gap-2"
        >
          <DollarSign className="h-4 w-4" />
          Invoices
        </Button>
        <Button
          variant={selectedData === "cargo" ? "default" : "outline"}
          onClick={() => setSelectedData("cargo")}
          className="gap-2"
        >
          <Package className="h-4 w-4" />
          Cargo
        </Button>
        <Button
          variant={selectedData === "transactions" ? "default" : "outline"}
          onClick={() => setSelectedData("transactions")}
          className="gap-2"
        >
          <FileText className="h-4 w-4" />
          Transactions
        </Button>
      </div>

      {/* Export Buttons */}
      <div className="flex flex-wrap gap-3">
        <Button
          onClick={() => invoiceExport.openInvoiceExport(sampleInvoices)}
          className="gap-2"
        >
          <Download className="h-4 w-4" />
          Export Invoices
        </Button>
        <Button
          onClick={() => cargoExport.openCargoExport(sampleCargo)}
          variant="outline"
          className="gap-2"
        >
          <Download className="h-4 w-4" />
          Export Cargo
        </Button>
        <Button
          onClick={() =>
            transactionExport.openTransactionExport(sampleTransactions)
          }
          variant="outline"
          className="gap-2"
        >
          <Download className="h-4 w-4" />
          Export Transactions
        </Button>
        <Button
          onClick={() =>
            genericExport.openGenericExport(
              getCurrentData(),
              `${selectedData.charAt(0).toUpperCase() + selectedData.slice(1)} Export`,
              {
                onSuccess: handleExportSuccess,
                onError: handleExportError,
              }
            )
          }
          variant="secondary"
          className="gap-2"
        >
          <Download className="h-4 w-4" />
          Generic Export
        </Button>
      </div>

      {/* Data Preview */}
      <div className="border rounded-lg overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">
              Current Data:{" "}
              {selectedData.charAt(0).toUpperCase() + selectedData.slice(1)}
            </h3>
            <Badge variant="secondary">{getCurrentData().length} records</Badge>
          </div>
        </div>
        <div className="max-h-96 overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {getCurrentHeaders().map((header) => (
                  <TableHead key={header} className="text-xs font-medium">
                    {header
                      .replace(/_/g, " ")
                      .replace(/\b\w/g, (l) => l.toUpperCase())}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {getCurrentData().map((row, index) => (
                <TableRow key={index}>
                  {getCurrentHeaders().map((header) => (
                    <TableCell key={header} className="text-xs">
                      {typeof row[header] === "object" && row[header] !== null
                        ? JSON.stringify(row[header])
                        : String(row[header] || "")}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Export Format Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="border rounded-lg p-4">
          <div className="flex items-center gap-3 mb-2">
            <FileSpreadsheet className="h-5 w-5 text-green-600" />
            <h4 className="font-medium">Excel Export</h4>
          </div>
          <p className="text-sm text-gray-600">
            Exports data as an Excel spreadsheet (.xlsx) with proper formatting,
            column headers, and data types. Perfect for analysis and
            calculations.
          </p>
        </div>
        <div className="border rounded-lg p-4">
          <div className="flex items-center gap-3 mb-2">
            <FileText className="h-5 w-5 text-red-600" />
            <h4 className="font-medium">PDF Export</h4>
          </div>
          <p className="text-sm text-gray-600">
            Exports data as a formatted PDF document with tables, headers, and
            professional styling. Ideal for sharing and printing.
          </p>
        </div>
      </div>

      {/* Export Dialogs */}
      <ExportDialog
        isOpen={invoiceExport.isOpen}
        onClose={invoiceExport.closeExportDialog}
        data={invoiceExport.data}
        {...invoiceExport.config}
      />
      <ExportDialog
        isOpen={cargoExport.isOpen}
        onClose={cargoExport.closeExportDialog}
        data={cargoExport.data}
        {...cargoExport.config}
      />
      <ExportDialog
        isOpen={transactionExport.isOpen}
        onClose={transactionExport.closeExportDialog}
        data={transactionExport.data}
        {...transactionExport.config}
      />
      <ExportDialog
        isOpen={genericExport.isOpen}
        onClose={genericExport.closeExportDialog}
        data={genericExport.data}
        {...genericExport.config}
      />
    </div>
  );
}
