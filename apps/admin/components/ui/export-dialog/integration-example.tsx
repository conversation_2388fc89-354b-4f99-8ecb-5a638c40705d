/**
 * Integration examples showing how to add export functionality to existing components
 */

"use client";

import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@workspace/ui/components/dropdown-menu";
import {
  Download,
  FileSpreadsheet,
  FileText,
  MoreHorizontal,
  Filter,
  Search,
} from "lucide-react";
import { ExportDialog } from ".";
import { useInvoiceExportDialog } from "./useExportDialog";

// Example 1: Adding export to a table header
export function TableWithExport({
  data,
  title,
}: {
  data: any[];
  title: string;
}) {
  const exportDialog = useInvoiceExportDialog();

  return (
    <div className="space-y-4">
      {/* Table Header with Export */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">{title}</h2>
          <p className="text-sm text-gray-600">{data.length} records</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="gap-2">
            <Search className="h-4 w-4" />
            Search
          </Button>
          <Button variant="outline" size="sm" className="gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button
            onClick={() => exportDialog.openInvoiceExport(data)}
            size="sm"
            className="gap-2"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Your existing table component would go here */}
      <div className="border rounded-lg p-4 text-center text-gray-500">
        Your existing table component
      </div>

      {/* Export Dialog */}
      <ExportDialog
        isOpen={exportDialog.isOpen}
        onClose={exportDialog.closeExportDialog}
        data={exportDialog.data}
        {...exportDialog.config}
      />
    </div>
  );
}

// Example 2: Adding export to a dropdown menu
export function DropdownWithExport({ data }: { data: any[] }) {
  const exportDialog = useInvoiceExportDialog();

  return (
    <div className="flex items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem>
            <Search className="h-4 w-4 mr-2" />
            Search Records
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Filter className="h-4 w-4 mr-2" />
            Apply Filters
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => exportDialog.openInvoiceExport(data)}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <ExportDialog
        isOpen={exportDialog.isOpen}
        onClose={exportDialog.closeExportDialog}
        data={exportDialog.data}
        {...exportDialog.config}
      />
    </div>
  );
}

// Example 3: Bulk actions with export
export function BulkActionsWithExport({
  selectedItems,
  allData,
}: {
  selectedItems: string[];
  allData: any[];
}) {
  const exportDialog = useInvoiceExportDialog();

  const handleExportSelected = () => {
    const selectedData = allData.filter((item) =>
      selectedItems.includes(item.id)
    );
    exportDialog.openInvoiceExport(selectedData);
  };

  const handleExportAll = () => {
    exportDialog.openInvoiceExport(allData);
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600">
        {selectedItems.length} selected
      </span>

      <Button
        variant="outline"
        size="sm"
        onClick={handleExportSelected}
        disabled={selectedItems.length === 0}
        className="gap-2"
      >
        <Download className="h-4 w-4" />
        Export Selected
      </Button>

      <Button
        variant="outline"
        size="sm"
        onClick={handleExportAll}
        className="gap-2"
      >
        <Download className="h-4 w-4" />
        Export All
      </Button>

      <ExportDialog
        isOpen={exportDialog.isOpen}
        onClose={exportDialog.closeExportDialog}
        data={exportDialog.data}
        {...exportDialog.config}
      />
    </div>
  );
}

// Example 4: Quick export buttons
export function QuickExportButtons({
  data,
  entityType,
}: {
  data: any[];
  entityType: string;
}) {
  const exportDialog = useInvoiceExportDialog();

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium">Quick Export:</span>

      <Button
        variant="outline"
        size="sm"
        onClick={() => exportDialog.openInvoiceExport(data)}
        className="gap-2"
      >
        <FileSpreadsheet className="h-4 w-4" />
        Excel
      </Button>

      {/* For direct PDF export without dialog */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => {
          // Direct PDF export logic here
          // TODO: Implement direct PDF export
        }}
        className="gap-2"
      >
        <FileText className="h-4 w-4" />
        PDF
      </Button>

      <ExportDialog
        isOpen={exportDialog.isOpen}
        onClose={exportDialog.closeExportDialog}
        data={exportDialog.data}
        {...exportDialog.config}
      />
    </div>
  );
}

// Example 5: Integration with existing management hooks
export function ManagementHookIntegration() {
  // This would typically be in your existing management hook
  const exportDialog = useInvoiceExportDialog();

  // Example function to add to your existing hooks
  const handleBulkExport = (selectedIds: string[], allData: any[]) => {
    const selectedData = allData.filter((item) =>
      selectedIds.includes(item.id)
    );
    exportDialog.openInvoiceExport(selectedData);
  };

  // Example function for filtered export
  const handleFilteredExport = (filteredData: any[]) => {
    exportDialog.openInvoiceExport(filteredData);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Management Hook Integration</h3>

      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium mb-2">
          Add to your existing management hooks:
        </h4>
        <pre className="text-xs bg-white p-3 rounded border overflow-x-auto">
          {`// In your management hook (e.g., useInvoiceManagement)
const exportDialog = useInvoiceExportDialog();

const handleBulkExport = useCallback((selectedIds: string[]) => {
  const selectedData = data.filter(item => selectedIds.includes(item.id));
  exportDialog.openInvoiceExport(selectedData);
}, [data, exportDialog]);

const handleFilteredExport = useCallback(() => {
  exportDialog.openInvoiceExport(filteredData);
}, [filteredData, exportDialog]);

// Return these functions along with your existing ones
return {
  // ... existing returns
  handleBulkExport,
  handleFilteredExport,
  exportDialog
};`}
        </pre>
      </div>

      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium mb-2">In your component:</h4>
        <pre className="text-xs bg-white p-3 rounded border overflow-x-auto">
          {`// In your component
const { handleBulkExport, exportDialog } = useInvoiceManagement();

// Add export button to your UI
<Button onClick={() => handleBulkExport(selectedIds)}>
  Export Selected
</Button>

// Add the dialog to your component
<ExportDialog
  isOpen={exportDialog.isOpen}
  onClose={exportDialog.closeExportDialog}
  data={exportDialog.data}
  {...exportDialog.config}
/>`}
        </pre>
      </div>

      <ExportDialog
        isOpen={exportDialog.isOpen}
        onClose={exportDialog.closeExportDialog}
        data={exportDialog.data}
        {...exportDialog.config}
      />
    </div>
  );
}

// Example 6: Complete component with export functionality
export function CompleteExampleComponent() {
  const exportDialog = useInvoiceExportDialog();

  // Sample data
  const sampleData = [
    { id: "1", name: "Item 1", value: 100, status: "Active" },
    { id: "2", name: "Item 2", value: 200, status: "Pending" },
    { id: "3", name: "Item 3", value: 300, status: "Completed" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Complete Example</h2>
        <Button
          onClick={() => exportDialog.openInvoiceExport(sampleData)}
          className="gap-2"
        >
          <Download className="h-4 w-4" />
          Export Data
        </Button>
      </div>

      <div className="border rounded-lg p-4">
        <p className="text-gray-600">
          This is a complete example showing how to integrate the export dialog
          into any existing component. The export button will open a dialog
          allowing users to choose between Excel and PDF formats.
        </p>
      </div>

      <ExportDialog
        isOpen={exportDialog.isOpen}
        onClose={exportDialog.closeExportDialog}
        data={exportDialog.data}
        title="Sample Data Export"
        filename="sample_data_export"
        {...exportDialog.config}
      />
    </div>
  );
}
