// Example usage of the enhanced FilterPanel component
import { useState } from "react";
import {
  FilterPanel,
  Column<PERSON><PERSON>er,
  FilterConfig,
  TableColumn,
} from "./filter-panel";

// Example 1: Static configuration (traditional approach)
const staticFilters: FilterConfig[] = [
  {
    key: "status",
    label: "Status",
    type: "text", // Changed from select to text
    searchable: true,
  },
  {
    key: "created_at",
    label: "Created Date",
    type: "date",
    searchable: true,
  },
  {
    key: "name",
    label: "Name",
    type: "text",
    searchable: true,
  },
];

// Example 2: Dynamic table data
const sampleTableData = [
  {
    id: 1,
    name: "<PERSON>",
    status: "active",
    department: "Engineering",
    salary: 75000,
    created_at: "2024-01-15",
  },
  {
    id: 2,
    name: "<PERSON>",
    status: "inactive",
    department: "Marketing",
    salary: 65000,
    created_at: "2024-02-20",
  },
  {
    id: 3,
    name: "<PERSON>",
    status: "pending",
    department: "Sales",
    salary: 55000,
    created_at: "2024-03-10",
  },
  {
    id: 4,
    name: "<PERSON>",
    status: "active",
    department: "Engineering",
    salary: 80000,
    created_at: "2024-01-25",
  },
];

// Example 3: Explicit column definitions
const tableColumns: TableColumn[] = [
  { key: "name", label: "Employee Name", type: "string", searchable: true },
  { key: "status", label: "Status", type: "enum", searchable: true },
  { key: "department", label: "Department", type: "enum", searchable: true },
  { key: "salary", label: "Salary", type: "number", searchable: true },
  { key: "created_at", label: "Hire Date", type: "date", searchable: true },
  { key: "id", label: "ID", type: "number", searchable: false }, // Not searchable
];

export function FilterPanelExamples() {
  const [searchTerm, setSearchTerm] = useState("");
  const [columnFilters, setColumnFilters] = useState<ColumnFilter[]>([]);

  const handleColumnFilterAdd = (filter: ColumnFilter) => {
    setColumnFilters((prev) => [...prev, filter]);
  };

  const handleColumnFilterRemove = (index: number) => {
    setColumnFilters((prev) => prev.filter((_, i) => i !== index));
  };

  // Custom value search function
  const handleValueSearch = async (
    column: string,
    query: string
  ): Promise<string[]> => {
    // Simulate API call or custom logic
    const values = sampleTableData
      .map((row) => String(row[column as keyof typeof row]))
      .filter((val) => val && val.toLowerCase().includes(query.toLowerCase()));

    return [...new Set(values)].sort();
  };

  return (
    <div className="space-y-8 p-6">
      <h1 className="text-2xl font-bold">FilterPanel Examples</h1>

      {/* Example 1: Static Configuration */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">1. Static Configuration</h2>
        <p className="text-gray-600">
          Traditional approach with predefined filter configurations.
        </p>
        <FilterPanel
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          filters={staticFilters}
          columnFilters={columnFilters}
          onColumnFilterAdd={handleColumnFilterAdd}
          onColumnFilterRemove={handleColumnFilterRemove}
        />
      </div>

      {/* Example 2: Dynamic from Table Data with Default Column */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">
          2. Dynamic from Table Data with Default Column
        </h2>
        <p className="text-gray-600">
          Automatically detects columns and types from table data. Defaults to
          "name" column selection.
        </p>
        <FilterPanel
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          columnFilters={columnFilters}
          onColumnFilterAdd={handleColumnFilterAdd}
          onColumnFilterRemove={handleColumnFilterRemove}
          // Dynamic props
          enableDynamicFilters={true}
          tableData={sampleTableData}
          defaultFilterColumn="name"
          autoSelectDefaultColumn={true}
        />
      </div>

      {/* Example 3: Explicit Column Definitions */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">
          3. Explicit Column Definitions
        </h2>
        <p className="text-gray-600">
          Uses explicit column definitions with custom value extraction from
          table data.
        </p>
        <FilterPanel
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          columnFilters={columnFilters}
          onColumnFilterAdd={handleColumnFilterAdd}
          onColumnFilterRemove={handleColumnFilterRemove}
          // Dynamic props
          enableDynamicFilters={true}
          columns={tableColumns}
          tableData={sampleTableData}
        />
      </div>

      {/* Example 4: Custom Value Search */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">4. Custom Value Search</h2>
        <p className="text-gray-600">
          Uses custom value search function for advanced filtering scenarios
          (e.g., API calls).
        </p>
        <FilterPanel
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          columnFilters={columnFilters}
          onColumnFilterAdd={handleColumnFilterAdd}
          onColumnFilterRemove={handleColumnFilterRemove}
          // Dynamic props
          enableDynamicFilters={true}
          columns={tableColumns}
          onValueSearch={handleValueSearch}
          defaultFilterColumn="name"
          autoSelectDefaultColumn={true}
        />
      </div>

      {/* Display Active Filters */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Active Filters</h2>
        <div className="bg-gray-50 p-4 rounded-lg">
          <p>
            <strong>Search Term:</strong> {searchTerm || "None"}
          </p>
          <p>
            <strong>Column Filters:</strong>
          </p>
          {Array.isArray(columnFilters) && columnFilters.length > 0 ? (
            <ul className="list-disc list-inside">
              {columnFilters.map((filter, index) => (
                <li key={index}>
                  {filter.label}: {filter.value}
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500">No column filters applied</p>
          )}
        </div>
      </div>
    </div>
  );
}

// Usage in different contexts:

// 1. Simple table with minimal setup
export function SimpleTableFilter({ data }: { data: any[] }) {
  const [searchTerm, setSearchTerm] = useState("");
  const [columnFilters, setColumnFilters] = useState<ColumnFilter[]>([]);

  return (
    <FilterPanel
      searchTerm={searchTerm}
      onSearchChange={setSearchTerm}
      columnFilters={columnFilters}
      onColumnFilterAdd={(filter) =>
        setColumnFilters((prev) => [...prev, filter])
      }
      onColumnFilterRemove={(index) =>
        setColumnFilters((prev) => prev.filter((_, i) => i !== index))
      }
      enableDynamicFilters={true}
      tableData={data}
      defaultFilterColumn="name"
      autoSelectDefaultColumn={true}
    />
  );
}

// 2. Advanced table with custom columns
export function AdvancedTableFilter({
  data,
  columns,
  onSearch,
}: {
  data: any[];
  columns: TableColumn[];
  onSearch: (column: string, query: string) => Promise<string[]>;
}) {
  const [searchTerm, setSearchTerm] = useState("");
  const [columnFilters, setColumnFilters] = useState<ColumnFilter[]>([]);

  return (
    <FilterPanel
      searchTerm={searchTerm}
      onSearchChange={setSearchTerm}
      columnFilters={columnFilters}
      onColumnFilterAdd={(filter) =>
        setColumnFilters((prev) => [...prev, filter])
      }
      onColumnFilterRemove={(index) =>
        setColumnFilters((prev) => prev.filter((_, i) => i !== index))
      }
      enableDynamicFilters={true}
      columns={columns}
      tableData={data}
      onValueSearch={onSearch}
    />
  );
}
