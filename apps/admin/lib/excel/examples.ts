/**
 * Excel Module Usage Examples
 * Demonstrates integration with existing Shamwaa Logistics modules
 */

import { ExcelGenerator, quickExport, EXCEL_THEMES } from "./index";

// Example 1: Invoice Export Integration
export async function exportInvoices(
  invoices: any[],
  options?: { includeLineItems?: boolean }
) {
  try {
    const data = {
      invoices: invoices.map((invoice) => ({
        inv_number: invoice.inv_number,
        customer_name: invoice.customer?.name || "Unknown",
        total: invoice.total,
        subtotal: invoice.subtotal,
        status: invoice.status,
        created_at: invoice.created_at,
        due_at: invoice.due_at,
        currency: invoice.currency || "USD",
      })),
    };

    if (options?.includeLineItems) {
      return await quickExport.template("Invoice Export", data);
    } else {
      return await quickExport.json(data.invoices, "invoices_export.xlsx");
    }
  } catch (error) {
    console.error("Invoice export failed:", error);
    return false;
  }
}

// Example 2: Financial Report Generation
export async function generateFinancialReport(
  transactions: any[],
  summary: Record<string, number>,
  dateRange: { start: string; end: string }
) {
  const generator = new ExcelGenerator();

  const reportData = {
    summary: {
      totalRevenue: summary.totalRevenue || 0,
      totalExpenses: summary.totalExpenses || 0,
      netIncome: (summary.totalRevenue || 0) - (summary.totalExpenses || 0),
      transactionCount: transactions.length,
    },
    transactions: transactions.map((tx) => ({
      id: tx.id,
      type: tx.type,
      amount: tx.amount,
      description: tx.description,
      created_at: tx.created_at,
      ledger_name: tx.ledger?.name || "Unknown",
      status: tx.status,
    })),
    totals: summary,
  };

  const filename = `financial_report_${dateRange.start}_to_${dateRange.end}.xlsx`;

  return await generator.generateFromTemplate("Financial Report", reportData, {
    filename,
  });
}

// Example 3: Cargo Manifest Export
export async function exportCargoManifest(batchId: string, cargo: any[]) {
  const manifestData = cargo.map((item) => ({
    tracking_number: item.tracking_number,
    china_tracking_number: item.china_tracking_number || "",
    description: item.description,
    customer_name: item.customer?.name || "Unknown",
    weight_value: item.weight_value,
    weight_unit: item.weight_unit,
    cbm_value: item.cbm_value,
    cbm_unit: item.cbm_unit,
    quantity: item.quantity || 1,
    status: item.status,
    code: item.batch?.code || batchId,
    created_at: item.created_at,
  }));

  const filename = `cargo_manifest_${batchId}_${new Date().toISOString().split("T")[0]}.xlsx`;

  return await quickExport.template(
    "Cargo Manifest",
    { cargo: manifestData },
    filename
  );
}

// Example 4: Shipment Tracking Report
export async function exportShipmentTracking(shipments: any[]) {
  const trackingData = shipments.map((shipment) => ({
    tracking_number: shipment.tracking_number,
    bill_of_lading: shipment.bill_of_lading,
    freight_name: shipment.freight?.name || "Unknown",
    code: shipment.batch?.code || "",
    status: shipment.status,
    current_location: shipment.metadata?.locations?.[0]?.name || "Unknown",
    origin:
      shipment.metadata?.locations?.find((l: any) => l.type === "origin")
        ?.name || "",
    destination:
      shipment.metadata?.locations?.find((l: any) => l.type === "destination")
        ?.name || "",
    created_at: shipment.created_at,
    updated_at: shipment.updated_at,
  }));

  return await quickExport.template("Shipment Tracking", {
    shipments: trackingData,
  });
}

// Example 5: Customer Statement Generation
export async function generateCustomerStatement(
  customer: any,
  transactions: any[],
  balance: number
) {
  const statementData = {
    customer: {
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      location: customer.location,
    },
    balance,
    transactions: transactions.map((tx) => ({
      date: tx.created_at,
      description: tx.description,
      type: tx.type,
      amount: tx.amount,
      balance: tx.running_balance || 0,
      reference: tx.reference || "",
    })),
  };

  const filename = `statement_${customer.name.replace(/\s+/g, "_")}_${new Date().toISOString().split("T")[0]}.xlsx`;

  return await quickExport.template(
    "Customer Statement",
    statementData,
    filename
  );
}

// Example 6: Multi-Sheet Logistics Report
export async function generateLogisticsReport(data: {
  shipments?: any[];
  cargo?: any[];
  batches?: any[];
  freights?: any[];
}) {
  const generator = new ExcelGenerator();

  const sources = [];

  if (data.shipments?.length) {
    sources.push({
      name: "Shipments",
      data: data.shipments,
      options: {
        theme: "LOGISTICS" as keyof typeof EXCEL_THEMES,
        excludeColumns: ["metadata", "attachments"],
        formatters: {
          created_at: (value: any) => (value ? new Date(value) : null),
          updated_at: (value: any) => (value ? new Date(value) : null),
        },
      },
    });
  }

  if (data.cargo?.length) {
    sources.push({
      name: "Cargo",
      data: data.cargo,
      options: {
        theme: "LOGISTICS" as keyof typeof EXCEL_THEMES,
        formatters: {
          weight_value: (value: any) => Number(value) || 0,
          cbm_value: (value: any) => Number(value) || 0,
          created_at: (value: any) => (value ? new Date(value) : null),
        },
      },
    });
  }

  if (data.batches?.length) {
    sources.push({
      name: "Batches",
      data: data.batches,
      options: {
        theme: "LOGISTICS" as keyof typeof EXCEL_THEMES,
        formatters: {
          created_at: (value: any) => (value ? new Date(value) : null),
          updated_at: (value: any) => (value ? new Date(value) : null),
        },
      },
    });
  }

  if (data.freights?.length) {
    sources.push({
      name: "Freights",
      data: data.freights,
      options: {
        theme: "LOGISTICS" as keyof typeof EXCEL_THEMES,
        excludeColumns: ["scac_codes", "prefixes"],
        formatters: {
          created_at: (value: any) => (value ? new Date(value) : null),
        },
      },
    });
  }

  const filename = `logistics_report_${new Date().toISOString().split("T")[0]}.xlsx`;

  return await generator.generateMultiSheet(sources, { filename });
}

// Example 7: Performance Dashboard Export
export async function exportPerformanceDashboard(kpis: any, metrics: any[]) {
  const dashboardData = {
    kpis: Object.entries(kpis).reduce(
      (acc, [key, value]) => {
        acc[key] = {
          current: (value as any).current || 0,
          target: (value as any).target || 0,
          status: (value as any).status || "Unknown",
        };
        return acc;
      },
      {} as Record<string, any>
    ),
    metrics: metrics.map((metric) => ({
      date: metric.date,
      metric_name: metric.name,
      value: metric.value,
      category: metric.category || "General",
      unit: metric.unit || "",
    })),
  };

  return await quickExport.template("Performance Dashboard", dashboardData);
}

// Example 8: Bulk Data Export with Custom Formatting
export async function exportBulkData(
  entityType: string,
  data: any[],
  customColumns?: Record<string, string>
) {
  const generator = new ExcelGenerator();

  // Apply entity-specific formatting
  let theme: keyof typeof EXCEL_THEMES = "DEFAULT";
  let formatters: Record<string, (value: any) => any> = {};

  switch (entityType) {
    case "invoices":
    case "transactions":
      theme = "FINANCIAL";
      formatters = {
        amount: (value) => Number(value) || 0,
        total: (value) => Number(value) || 0,
        subtotal: (value) => Number(value) || 0,
      };
      break;
    case "shipments":
    case "cargo":
    case "batches":
      theme = "LOGISTICS";
      formatters = {
        weight_value: (value) => Number(value) || 0,
        cbm_value: (value) => Number(value) || 0,
      };
      break;
  }

  // Add common date formatters
  formatters.created_at = (value) => (value ? new Date(value) : null);
  formatters.updated_at = (value) => (value ? new Date(value) : null);
  formatters.due_at = (value) => (value ? new Date(value) : null);

  const filename = `${entityType}_export_${new Date().toISOString().split("T")[0]}.xlsx`;

  return await generator.generateFromJSON(data, {
    sheetName: entityType.charAt(0).toUpperCase() + entityType.slice(1),
    filename,
    theme,
    columnMapping: customColumns,
    formatters,
  });
}

// Example 9: Import and Process Excel Data
export async function importAndProcessExcel(
  file: File,
  entityType: string,
  processor: (data: any[]) => Promise<any>
) {
  const generator = new ExcelGenerator();

  try {
    const importResult = await generator.importExcel(file);

    if (!importResult.success || !importResult.data) {
      throw new Error(importResult.error || "Import failed");
    }

    // Process each worksheet
    const results = [];

    for (const worksheet of importResult.data.worksheets) {
      console.log(
        `Processing sheet: ${worksheet.name} (${worksheet.rowCount} rows)`
      );

      // Apply entity-specific processing
      const processedData = await processor(worksheet.data);
      results.push({
        sheetName: worksheet.name,
        processedCount: processedData.length,
        data: processedData,
      });
    }

    return {
      success: true,
      results,
      totalSheets: importResult.data.worksheets.length,
    };
  } catch (error) {
    console.error(`Excel import failed for ${entityType}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Example 10: Scheduled Report Generation
export async function generateScheduledReport(
  reportType: string,
  dataFetcher: () => Promise<any>,
  schedule: { frequency: "daily" | "weekly" | "monthly"; time: string }
) {
  try {
    console.log(`Generating ${reportType} report (${schedule.frequency})`);

    const data = await dataFetcher();
    const timestamp = new Date().toISOString().split("T")[0];
    const filename = `${reportType}_${schedule.frequency}_${timestamp}.xlsx`;

    let result;

    switch (reportType) {
      case "financial":
        result = await generateFinancialReport(
          data.transactions,
          data.summary,
          data.dateRange
        );
        break;
      case "logistics":
        result = await generateLogisticsReport(data);
        break;
      case "performance":
        result = await exportPerformanceDashboard(data.kpis, data.metrics);
        break;
      default:
        result = await exportBulkData(reportType, data);
    }

    if (result?.success) {
      return result;
    } else {
      throw new Error(result?.error || "Report generation failed");
    }
  } catch (error) {
    throw error;
  }
}
