/**
 * Example implementations for integrating the invoice template generator
 * with the release authorization system.
 */

import { 
  generateReleaseDocument,
  generateBatchReleaseDocument,
  generateStandardReleaseDocument,
  generateQuickReleaseDocument,
  generateDetailedManifest,
  createPersonnelInfo,
  TEMPLATE_PRESETS,
  validateReleaseData
} from './index';

import type { 
  ShipmentForRelease,
  ReleaseAuthorizationData,
  CargoItem,
  FreightInfo,
  BatchInfo
} from './index';

/**
 * Example: Generate release document from the authorization modal
 */
export async function handleGenerateReleaseDocument(
  shipment: ShipmentForRelease,
  currentUser: {
    id: string;
    name: string;
    role?: { name: string; department?: { name: string } };
  },
  options?: {
    documentType?: 'standard' | 'quick' | 'detailed';
    notes?: string;
    specialInstructions?: string;
    autoDownload?: boolean;
  }
) {
  try {
    const personnelInfo = createPersonnelInfo(currentUser);
    
    const additionalData = {
      notes: options?.notes,
      specialInstructions: options?.specialInstructions
    };

    let result;

    switch (options?.documentType) {
      case 'quick':
        result = await generateQuickReleaseDocument(shipment, personnelInfo, {
          additionalData,
          autoDownload: options?.autoDownload
        });
        break;
      
      case 'detailed':
        result = await generateDetailedManifest(shipment, personnelInfo, {
          additionalData,
          autoDownload: options?.autoDownload
        });
        break;
      
      default:
        result = await generateStandardReleaseDocument(shipment, personnelInfo, {
          additionalData,
          autoDownload: options?.autoDownload
        });
        break;
    }

    return result;

  } catch (error) {
    console.error('Error generating release document:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Example: Generate batch release document with multiple cargo items
 */
export async function handleGenerateBatchDocument(
  batchData: {
    batchId: string;
    batchCode: string;
    freightId: string;
    freightName: string;
    origin: string;
    destination: string;
    cargoItems: Array<{
      id: string;
      trackingNumber: string;
      description: string;
      customerName: string;
      weight: number;
      value: number;
      quantity?: number;
      category?: string;
      customerCompany?: string;
      customerPhone?: string;
    }>;
  },
  currentUser: {
    id: string;
    name: string;
    role?: { name: string; department?: { name: string } };
  },
  options?: {
    notes?: string;
    specialInstructions?: string;
    autoDownload?: boolean;
  }
) {
  try {
    const personnelInfo = createPersonnelInfo(currentUser);

    // Convert cargo data to CargoItem format
    const cargoItems: CargoItem[] = batchData.cargoItems.map(item => ({
      id: item.id,
      trackingNumber: item.trackingNumber,
      description: item.description,
      category: item.category || 'General',
      quantity: item.quantity || 1,
      weight: item.weight,
      weightUnit: 'kg',
      value: item.value,
      currency: 'USD',
      customerName: item.customerName,
      customerCompany: item.customerCompany,
      customerPhone: item.customerPhone
    }));

    // Create freight info
    const freight: FreightInfo = {
      id: batchData.freightId,
      name: batchData.freightName,
      type: 'SEA', // This should be determined from actual data
      origin: batchData.origin,
      destination: batchData.destination
    };

    // Calculate totals
    const totalWeight = cargoItems.reduce((sum, item) => sum + item.weight, 0);
    const totalValue = cargoItems.reduce((sum, item) => sum + item.value, 0);

    const result = await generateBatchReleaseDocument(
      {
        batchId: batchData.batchId,
        batchCode: batchData.batchCode,
        freight,
        cargoItems,
        payment: {
          status: 'PAID',
          totalAmount: totalValue,
          paidAmount: totalValue,
          currency: 'USD'
        }
      },
      personnelInfo,
      {
        notes: options?.notes,
        specialInstructions: options?.specialInstructions,
        autoDownload: options?.autoDownload,
        templateOptions: TEMPLATE_PRESETS.BATCH_RELEASE
      }
    );

    return result;

  } catch (error) {
    console.error('Error generating batch document:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Example: React component integration
 */
export const useReleaseDocumentGenerator = () => {
  const generateDocument = async (
    shipment: ShipmentForRelease,
    user: any,
    options?: {
      type?: 'standard' | 'quick' | 'detailed';
      notes?: string;
      specialInstructions?: string;
    }
  ) => {
    try {
      const result = await handleGenerateReleaseDocument(shipment, user, {
        documentType: options?.type || 'standard',
        notes: options?.notes,
        specialInstructions: options?.specialInstructions,
        autoDownload: true
      });

      if (result.success) {
        // Show success notification
        console.log('Document generated successfully:', result.data?.fileName);
        return { success: true, fileName: result.data?.fileName };
      } else {
        // Show error notification
        console.error('Failed to generate document:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      return { 
        success: false, 
        error: 'An unexpected error occurred while generating the document' 
      };
    }
  };

  const generateBatchDocument = async (
    batchData: Parameters<typeof handleGenerateBatchDocument>[0],
    user: any,
    options?: {
      notes?: string;
      specialInstructions?: string;
    }
  ) => {
    try {
      const result = await handleGenerateBatchDocument(batchData, user, {
        notes: options?.notes,
        specialInstructions: options?.specialInstructions,
        autoDownload: true
      });

      if (result.success) {
        console.log('Batch document generated successfully:', result.data?.fileName);
        return { success: true, fileName: result.data?.fileName };
      } else {
        console.error('Failed to generate batch document:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      return { 
        success: false, 
        error: 'An unexpected error occurred while generating the batch document' 
      };
    }
  };

  return {
    generateDocument,
    generateBatchDocument
  };
};

/**
 * Example: Validation before document generation
 */
export function validateShipmentForDocument(shipment: ShipmentForRelease): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  if (!shipment.id) {
    errors.push('Shipment ID is required');
  }

  if (!shipment.customer) {
    errors.push('Customer information is required');
  }

  if (!shipment.cargoType) {
    errors.push('Cargo type is required');
  }

  if (!shipment.trackingNumber) {
    warnings.push('Tracking number is missing - will be auto-generated');
  }

  // Status validation
  if (shipment.status !== 'pending' && shipment.status !== 'authorized') {
    warnings.push(`Shipment status is '${shipment.status}' - ensure this is correct`);
  }

  // Documentation validation
  if (!shipment.docsComplete) {
    warnings.push('Documents are not marked as complete');
  }

  if (!shipment.paymentComplete) {
    warnings.push('Payment is not marked as complete');
  }

  // Weight validation
  if (!shipment.weight || shipment.weight === '0') {
    warnings.push('Weight information is missing or zero');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Example: Custom document data preparation
 */
export function prepareCustomReleaseData(
  shipment: ShipmentForRelease,
  customData: {
    cargoItems?: Array<{
      description: string;
      quantity: number;
      weight: number;
      value: number;
      customerName: string;
      category?: string;
    }>;
    freightInfo?: {
      vessel?: string;
      departureDate?: string;
      arrivalDate?: string;
    };
    paymentInfo?: {
      method?: string;
      reference?: string;
      dueDate?: string;
    };
  }
): Parameters<typeof generateReleaseDocument>[2] {
  const additionalData: Parameters<typeof generateReleaseDocument>[2] = {};

  // Add custom cargo items if provided
  if (customData.cargoItems) {
    additionalData.cargoItems = customData.cargoItems.map((item, index) => ({
      id: `${shipment.id}-cargo-${index + 1}`,
      trackingNumber: `${shipment.trackingNumber || 'TRK'}-${index + 1}`,
      description: item.description,
      category: item.category || 'General',
      quantity: item.quantity,
      weight: item.weight,
      weightUnit: 'kg',
      value: item.value,
      currency: 'USD',
      customerName: item.customerName
    }));
  }

  // Add freight information
  if (customData.freightInfo) {
    additionalData.freight = {
      vessel: customData.freightInfo.vessel,
      departureDate: customData.freightInfo.departureDate,
      arrivalDate: customData.freightInfo.arrivalDate || shipment.arrivalDate
    };
  }

  // Add payment information
  if (customData.paymentInfo) {
    additionalData.payment = {
      status: shipment.paymentComplete ? 'PAID' : 'PENDING',
      totalAmount: additionalData.cargoItems?.reduce((sum, item) => sum + item.value, 0) || 1000,
      paidAmount: shipment.paymentComplete ? 
        (additionalData.cargoItems?.reduce((sum, item) => sum + item.value, 0) || 1000) : 0,
      currency: 'USD',
      paymentMethod: customData.paymentInfo.method,
      paymentReference: customData.paymentInfo.reference,
      dueDate: customData.paymentInfo.dueDate
    };
  }

  return additionalData;
}

/**
 * Example: Bulk document generation for multiple shipments
 */
export async function generateBulkReleaseDocuments(
  shipments: ShipmentForRelease[],
  user: any,
  options?: {
    documentType?: 'standard' | 'quick' | 'detailed';
    notes?: string;
    specialInstructions?: string;
  }
): Promise<{
  successful: Array<{ shipmentId: string; fileName: string }>;
  failed: Array<{ shipmentId: string; error: string }>;
}> {
  const successful: Array<{ shipmentId: string; fileName: string }> = [];
  const failed: Array<{ shipmentId: string; error: string }> = [];

  for (const shipment of shipments) {
    try {
      const result = await handleGenerateReleaseDocument(shipment, user, {
        documentType: options?.documentType,
        notes: options?.notes,
        specialInstructions: options?.specialInstructions,
        autoDownload: false // Don't auto-download for bulk operations
      });

      if (result.success && result.data) {
        successful.push({
          shipmentId: shipment.id,
          fileName: result.data.fileName
        });
      } else {
        failed.push({
          shipmentId: shipment.id,
          error: result.error || 'Unknown error'
        });
      }
    } catch (error) {
      failed.push({
        shipmentId: shipment.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Add a small delay to prevent overwhelming the system
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  return { successful, failed };
}
