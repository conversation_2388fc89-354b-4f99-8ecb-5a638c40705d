# Bulk Fetch Service

The Bulk Fetch Service is a performance-optimized service for fetching multiple entity types with configurable detail levels and built-in caching. It replaces individual entity service calls with a unified, efficient approach.

## Features

- **Performance Optimization**: Single query per entity type with optimized joins
- **Detail Level Control**: Minimal, Standard, and Detailed views for different use cases
- **Built-in Caching**: 5-minute TTL cache with automatic cleanup
- **Batch Processing**: Handle large datasets efficiently
- **Parallel Fetching**: Fetch multiple entity types simultaneously
- **Consistent API**: Maintains existing payload structure

## Usage

### Basic Entity Fetching

```typescript
import { bulkFetchService, DetailLevel } from "@/lib/logistics";

// Fetch customers with minimal details
const result = await bulkFetchService.fetchEntityType(
  "customers",
  { limit: 100, filters: { status: "ACTIVE" } },
  { detailLevel: DetailLevel.MINIMAL }
);

// Fetch cargos with full relations
const cargosResult = await bulkFetchService.fetchEntityType(
  "cargos",
  { limit: 50 },
  { detailLevel: DetailLevel.DETAILED }
);
```

### Specialized Methods

```typescript
// Get customers with cargo statistics
const customers = await bulkFetchService.getAllCustomersWithCargoStats({
  limit: 100,
  filters: { status: "ACTIVE" },
});

// Get cargos with full relations
const cargos = await bulkFetchService.getAllCargosWithRelations({
  limit: 50,
  filters: { batch_id: "batch-123" },
});

// Get batches with cargo information
const batches = await bulkFetchService.getAllBatchesWithCargo({
  limit: 25,
});

// Get freights with relations
const freights = await bulkFetchService.getAllFreightsWithRelations();

// Get shipments with relations
const shipments = await bulkFetchService.getAllShipmentsWithRelations();
```

### Fetch Entities by Specific IDs

```typescript
// Fetch specific customers by their IDs
const customerIds = ["customer-1", "customer-2", "customer-3"];
const customers = await bulkFetchService.fetchEntitiesByIds(
  "customers",
  customerIds,
  { detailLevel: DetailLevel.DETAILED }
);

// Fetch specific cargos by their IDs with minimal details
const cargoIds = ["cargo-1", "cargo-2"];
const cargos = await bulkFetchService.fetchEntitiesByIds("cargos", cargoIds, {
  detailLevel: DetailLevel.MINIMAL,
});
```

### Multiple Entity Types

```typescript
// Fetch multiple entity types in parallel
const results = await bulkFetchService.fetchMultipleEntityTypes(
  ["customers", "cargos", "batches"],
  { limit: 50 },
  { detailLevel: DetailLevel.STANDARD }
);

// Process results
results.forEach((result) => {
  if (result.success) {
    console.log(`${result.entityType}: ${result.count} records`);
  }
});
```

### Batch Processing for Large Datasets

```typescript
// Process large datasets in batches
const allCargos = await bulkFetchService.fetchInBatches(
  "cargos",
  { filters: { status: "ACTIVE" } },
  { batchSize: 1000, detailLevel: DetailLevel.STANDARD }
);
```

## Detail Levels

### Minimal

Returns only essential fields for performance-critical operations:

- **Customers**: id, name, email, phone, status
- **Cargos**: id, tracking_number, particular, status, weight_value, weight_unit
- **Batches**: id, name, code, status, batch_type

### Standard

Returns core fields with basic metadata:

- Includes all minimal fields plus timestamps, locations, and key business fields
- No relationship joins for better performance

### Detailed

Returns full entity data with all relationships:

- **Customers**: Includes cargo count statistics
- **Cargos**: Includes customers, batches, suppliers, accounts, and assigned users
- **Batches**: Includes cargo list and account information
- **Freights**: Includes shipments and account information
- **Shipments**: Includes freight, batch, and account information

## Configuration Options

```typescript
interface BulkFetchOptions {
  detailLevel?: DetailLevel; // MINIMAL | STANDARD | DETAILED
  includeInactive?: boolean; // Include INACTIVE status records
  batchSize?: number; // For batch processing (default: 1000)
  cacheResults?: boolean; // Enable/disable caching (default: true)
}
```

## Caching

The service includes built-in caching with:

- **TTL**: 5 minutes
- **Automatic Cleanup**: Expired entries are removed automatically
- **Cache Key**: Based on entity type, parameters, and options
- **Manual Control**: Clear cache or get statistics

```typescript
// Clear all cached data
bulkFetchService.clearCache();

// Get cache statistics
const stats = bulkFetchService.getCacheStats();
console.log(`Cache size: ${stats.size}, Keys: ${stats.keys.join(", ")}`);
```

## Performance Benefits

1. **Reduced Query Count**: Single optimized query per entity type
2. **Selective Field Loading**: Only fetch required fields based on detail level
3. **Efficient Joins**: Optimized relationship loading for detailed views
4. **ID-Based Filtering**: Direct database filtering eliminates post-query filtering
5. **Caching**: Avoid repeated database calls for identical requests
6. **Batch Processing**: Handle large datasets without memory issues
7. **Parallel Execution**: Fetch multiple entity types simultaneously

### ID-Based Filtering Benefits

The `fetchEntitiesByIds` method provides significant performance improvements over post-filtering:

- **Database-Level Filtering**: Uses SQL `IN` clause for efficient ID filtering
- **Reduced Data Transfer**: Only fetches the exact records needed
- **No Post-Processing**: Eliminates client-side filtering overhead
- **Optimized Queries**: Leverages database indexes on ID columns

## Migration from Individual Services

The bulk fetch service provides drop-in replacements for existing service methods:

```typescript
// Before
const customers = await customerService.getAllCustomersWithCargoStats(params);
const cargos = await cargoService.getAllCargosWithRelations(params);
const batches = await batchService.getAllBatchesWithCargo(params);

// After
const customers = await bulkFetchService.getAllCustomersWithCargoStats(params);
const cargos = await bulkFetchService.getAllCargosWithRelations(params);
const batches = await bulkFetchService.getAllBatchesWithCargo(params);
```

## Error Handling

All methods return a consistent response structure:

```typescript
interface BulkFetchResult<T> {
  entityType: EntityType;
  data: T[];
  count: number;
  success: boolean;
  error?: string;
  cached?: boolean;
}
```

## Testing

The service includes comprehensive unit tests covering:

- Entity fetching with different detail levels
- Error handling
- Caching behavior
- Parallel fetching
- Specialized methods

Run tests with:

```bash
npm test bulk-fetch
```
