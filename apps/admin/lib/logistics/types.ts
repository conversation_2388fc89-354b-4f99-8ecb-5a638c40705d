import { Database } from "@/lib/types/supabase";
import type {
  UserInfo,
  CustomerInfo,
  AccountInfo,
  ServiceResponse,
  ServiceListResponse,
  BulkOperationResponse,
  QueryOptions,
  SearchOptions,
  PaginationParams,
  SortParams,
  WithAccount,
} from "@/lib/types/shared";

// Base types from database
export type StatusEnum = Database["public"]["Enums"]["status_enum"];
export type CategoryEnum = Database["public"]["Enums"]["category_enum"];
export type DimensionUnitEnum =
  Database["public"]["Enums"]["dimension_unit_enum"];
export type WeightUnitEnum = Database["public"]["Enums"]["weight_unit_enum"];
export type BatchTypeEnum = Database["public"]["Enums"]["batch_type_enum"];
export type FreightTypeEnum = Database["public"]["Enums"]["freight_type_enum"];
export type TrackingTypeEnum =
  Database["public"]["Enums"]["tracking_type_enum"];

// Entity types
export type User = Database["public"]["Tables"]["users"]["Row"];
export type Account = Database["public"]["Tables"]["accounts"]["Row"];
export type Role = Database["public"]["Tables"]["roles"]["Row"];
export type Department = Database["public"]["Tables"]["departments"]["Row"];
export type Session = Database["public"]["Tables"]["sessions"]["Row"];
export type Recovery = Database["public"]["Tables"]["recoveries"]["Row"];
export type Notification = Database["public"]["Tables"]["notifications"]["Row"];
export type Document = Database["public"]["Tables"]["documents"]["Row"];
export type Log = Database["public"]["Tables"]["logs"]["Row"];
export type Schedule = Database["public"]["Tables"]["schedules"]["Row"];
export type Assignment = Database["public"]["Tables"]["assignments"]["Row"];
export type Approval = Database["public"]["Tables"]["approvals"]["Row"];
export type Customer = Database["public"]["Tables"]["customers"]["Row"];
export type Cargo = Database["public"]["Tables"]["cargos"]["Row"];
export type Batch = Database["public"]["Tables"]["batches"]["Row"];
export type Freight = Database["public"]["Tables"]["freights"]["Row"];
export type Shipment = Database["public"]["Tables"]["shipments"]["Row"];
export type Handover = Database["public"]["Tables"]["handovers"]["Row"];
export type Ledger = Database["public"]["Tables"]["ledgers"]["Row"];
export type Transaction = Database["public"]["Tables"]["transactions"]["Row"];

// Insert types
export type UserInsert = Database["public"]["Tables"]["users"]["Insert"];
export type AccountInsert = Database["public"]["Tables"]["accounts"]["Insert"];
export type RoleInsert = Database["public"]["Tables"]["roles"]["Insert"];
export type DepartmentInsert =
  Database["public"]["Tables"]["departments"]["Insert"];
export type SessionInsert = Database["public"]["Tables"]["sessions"]["Insert"];
export type RecoveryInsert =
  Database["public"]["Tables"]["recoveries"]["Insert"];
export type NotificationInsert =
  Database["public"]["Tables"]["notifications"]["Insert"];
export type DocumentInsert =
  Database["public"]["Tables"]["documents"]["Insert"];
export type LogInsert = Database["public"]["Tables"]["logs"]["Insert"];
export type ScheduleInsert =
  Database["public"]["Tables"]["schedules"]["Insert"];
export type AssignmentInsert =
  Database["public"]["Tables"]["assignments"]["Insert"];
export type ApprovalInsert =
  Database["public"]["Tables"]["approvals"]["Insert"];
export type CustomerInsert =
  Database["public"]["Tables"]["customers"]["Insert"];
export type CargoInsert = Database["public"]["Tables"]["cargos"]["Insert"];
export type BatchInsert = Database["public"]["Tables"]["batches"]["Insert"];
export type FreightInsert = Database["public"]["Tables"]["freights"]["Insert"];
export type ShipmentInsert =
  Database["public"]["Tables"]["shipments"]["Insert"];
export type HandoverInsert =
  Database["public"]["Tables"]["handovers"]["Insert"];
export type LedgerInsert = Database["public"]["Tables"]["ledgers"]["Insert"];
export type TransactionInsert =
  Database["public"]["Tables"]["transactions"]["Insert"];

// Tag types (manually defined since tags table may not be in current schema)
export type TagInsert = {
  name: string;
  description?: string | null;
  color?: string | null;
  category?: string | null;
  usage_count?: number | null;
  status?: StatusEnum | null;
  account_id?: string | null;
};

// Update types
export type UserUpdate = Database["public"]["Tables"]["users"]["Update"];
export type AccountUpdate = Database["public"]["Tables"]["accounts"]["Update"];
export type RoleUpdate = Database["public"]["Tables"]["roles"]["Update"];
export type DepartmentUpdate =
  Database["public"]["Tables"]["departments"]["Update"];
export type SessionUpdate = Database["public"]["Tables"]["sessions"]["Update"];
export type RecoveryUpdate =
  Database["public"]["Tables"]["recoveries"]["Update"];
export type NotificationUpdate =
  Database["public"]["Tables"]["notifications"]["Update"];
export type DocumentUpdate =
  Database["public"]["Tables"]["documents"]["Update"];
export type LogUpdate = Database["public"]["Tables"]["logs"]["Update"];
export type ScheduleUpdate =
  Database["public"]["Tables"]["schedules"]["Update"];
export type AssignmentUpdate =
  Database["public"]["Tables"]["assignments"]["Update"];
export type ApprovalUpdate =
  Database["public"]["Tables"]["approvals"]["Update"];
export type CustomerUpdate =
  Database["public"]["Tables"]["customers"]["Update"];
export type CargoUpdate = Database["public"]["Tables"]["cargos"]["Update"];
export type BatchUpdate = Database["public"]["Tables"]["batches"]["Update"];
export type FreightUpdate = Database["public"]["Tables"]["freights"]["Update"];
export type ShipmentUpdate =
  Database["public"]["Tables"]["shipments"]["Update"];
export type HandoverUpdate =
  Database["public"]["Tables"]["handovers"]["Update"];
export type LedgerUpdate = Database["public"]["Tables"]["ledgers"]["Update"];
export type TransactionUpdate =
  Database["public"]["Tables"]["transactions"]["Update"];

export type TagUpdate = {
  name?: string;
  description?: string | null;
  color?: string | null;
  category?: string | null;
  usage_count?: number | null;
  status?: StatusEnum | null;
  account_id?: string | null;
};

// Common response interface
export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

// Common list response interface
export interface ServiceListResponse<T> {
  data: T[];
  error: string | null;
  success: boolean;
  count?: number;
}

// Pagination parameters
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

// Sorting parameters
export interface SortParams {
  column?: string;
  ascending?: boolean;
}

// Filter parameters
export interface FilterParams {
  status?: StatusEnum | StatusEnum[];
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  [key: string]: any;
}

// Query parameters combining pagination, sorting, and filtering
export interface QueryParams extends PaginationParams, SortParams {
  filters?: FilterParams;
}

// Extended entity types with relationships
export interface UserWithAccount extends User {
  accounts?: {
    id: string;
    email: string;
    status: StatusEnum | null;
    role_id: string | null;
    roles?: {
      id: string;
      name: string;
      departments?: {
        id: string;
        name: string;
      } | null;
    } | null;
  } | null;
}

export interface AccountWithUserAndRole extends Account {
  users?: User;
  roles?: RoleWithDepartment;
}

export interface RoleWithDepartment extends Role {
  departments?: Department;
}

export interface CargoWithRelations extends Cargo {
  customers?: Customer;
  batches?: Batch;
  users?: User; // receiver
  accounts?: Account;
  assigned_user?: User; // assigned user
}

// Extended interface for release authorization specific cargo data with documents
export interface CargoWithDocuments extends CargoWithRelations {
  documents?: (Document & { downloadUrl?: string | null })[];
  documentUrls?: string[];
}

export interface BatchWithRelations extends Batch {
  accounts?: Account;
  cargos?: Cargo[];
  shipments?: Shipment[];
}

export interface FreightWithRelations extends Freight {
  shipments?: Shipment[];
  accounts?: Account;
}

export interface ShipmentWithRelations extends Shipment {
  accounts?: Account;
  freights?: Freight;
  batches?: Batch;
}

export interface HandoverWithRelations extends Handover {
  sender?: Account;
  receiver?: Account;
  cargos?: Cargo;
}

// Entity types with account relations using shared type
export type NotificationWithAccount = WithAccount<Notification>;
export type DocumentWithAccount = WithAccount<Document>;
export type LogWithAccount = WithAccount<Log>;
export type ScheduleWithAccount = WithAccount<Schedule>;
export type AssignmentWithAccount = WithAccount<Assignment>;
export type ApprovalWithAccount = WithAccount<Approval>;

export type LedgerWithAccount = WithAccount<Ledger> & {
  transactions?: Transaction[];
};

export type TransactionWithLedgerAndAccount = WithAccount<Transaction> & {
  ledgers?: Ledger;
};

// Business logic interfaces
export interface CargoTrackingInfo {
  trackingNumber: string;
  status: StatusEnum;
  currentLocation?: string;
  estimatedDelivery?: string;
  lastUpdate: string;
}

export interface BatchCapacityInfo {
  totalWeight: number;
  totalCBM: number;
  cargoCount: number;
  utilizationPercentage: number;
}

export interface FreightCapacityInfo {
  currentLoad: number;
  maxCapacity: number;
  availableCapacity: number;
  utilizationPercentage: number;
}

export interface VerificationData {
  qrVerified: boolean;
  biometricVerified: boolean;
  location?: {
    latitude: number;
    longitude: number;
    timestamp: string;
  };
  timestamp: string;
  verifiedBy: string;
}

// Cargo Categories Enum - Global reference for cargo classification
// Maps to existing database category_enum values
export enum Cargo_Categories {
  DANGEROUS = "DANGEROUS",
  SAFE = "SAFE",
  COPY = "COPY",
}

// Cargo Categories display labels
export const CARGO_CATEGORY_LABELS = {
  [Cargo_Categories.DANGEROUS]: "Dangerous Goods",
  [Cargo_Categories.SAFE]: "Safe Goods",
  [Cargo_Categories.COPY]: "Copy",
} as const;

// Tracking Number Generator types
export type TrackingPrefix = "AIR" | "SEA" | "ROAD";
export type LocationCode =
  | "G"
  | "S"
  | "B"
  | "D"
  | "Q"
  | "C"
  | "N"
  | "W"
  | "H"
  | "X"
  | "T"
  | "F"
  | "Y"
  | "K"
  | "L"
  | "U"
  | "Z";
export type WeightCategory = "H" | "L"; // H=Heavy (≥100kg), L=Light (<100kg)

export interface TrackingNumberComponents {
  prefix: TrackingPrefix;
  locationCode: LocationCode;
  year: string;
  month: string;
  index: string;
  weightCategory: WeightCategory;
}

export interface TrackingNumberGenerationOptions {
  prefix: TrackingPrefix;
  origin: string;
  weight: number;
  weightUnit?: WeightUnitEnum;
}

export interface LocationMapping {
  [key: string]: LocationCode;
}

export interface TrackingNumberValidationResult {
  isValid: boolean;
  components?: TrackingNumberComponents;
  errors?: string[];
}

export interface CargoFormData {
  // Basic information
  particular: string;
  china_tracking_number?: string;
  category: CategoryEnum;
  status?: StatusEnum;

  // Customer and receiver
  customer_id: string;

  // Quantity
  quantity: number;

  // Dimensions
  dimension_length: number;
  dimension_width: number;
  dimension_height: number;
  dimension_unit: DimensionUnitEnum;
  cbm_value: number;
  cbm_unit: DimensionUnitEnum;

  // Weight
  weight_value: number;
  weight_unit: WeightUnitEnum;

  // Pricing
  unit_price: number;
  total_price: number;

  // System fields (auto-generated)
  tracking_number?: string;
  account_id?: string;
  batch_id?: string;
}

// Financial types
export type Invoice = {
  id: string;
  invoiceNumber: string;
  description: string | null;
  totalAmount: number;
  paidAmount: number | null;
  dueDate: string | null;
  status: "DRAFT" | "PENDING" | "PAID" | "OVERDUE" | "CANCELLED";
  cargo_id: string | null;
  customer_id: string;
  account_id: string;
  currency_conv_rate?: number | null; // Custom USD to TZS conversion rate
  shared?: boolean | null; // Track if invoice has been shared with customer
  created_at: string;
  updated_at: string;
};

export type CreateInvoiceData = Omit<
  Invoice,
  "id" | "created_at" | "updated_at"
>;
export type UpdateInvoiceData = Partial<CreateInvoiceData>;

// Using shared types from @/lib/types/shared

export interface InvoiceWithRelations extends Invoice {
  cargos?: CargoWithRelations | null;
  customers?: Customer | null;
  accounts?: AccountInfo | null;
}

export type CreateTransactionData = Omit<
  Transaction,
  "id" | "created_at" | "updated_at"
>;
export type UpdateTransactionData = Partial<CreateTransactionData>;

// Simplified invoice info type
type InvoiceInfo = {
  id: string;
  invoiceNumber: string;
  description: string | null;
  totalAmount: number;
  customers?: CustomerInfo | null;
};

export interface TransactionWithRelations extends Transaction {
  invoices?: InvoiceInfo | null;
  accounts?: AccountInfo | null;
}

// Using shared QueryOptions and SearchOptions from @/lib/types/shared

// Statistics and Analytics types
export interface CargoStatistics {
  totalCargos: number;
  activeCargos: number;
  deliveredCargos: number;
  pendingCargos: number;
  totalValue: number;
  averageWeight: number;
  averageVolume: number;
  statusDistribution: { [key in StatusEnum]?: number };
  categoryDistribution: { [key in CategoryEnum]?: number };
  monthlyTrends: {
    month: string;
    count: number;
    value: number;
  }[];
}

export interface DashboardMetrics {
  totalCargos: number;
  totalCustomers: number;
  totalValue: number;
  pendingDeliveries: number;
  recentActivity: {
    id: string;
    type: string;
    message: string;
    timestamp: string;
  }[];
}

// Utility types for form handling
export type CargoFormErrors = Partial<Record<keyof CargoFormData, string>>;
export type CargoFormTouched = Partial<Record<keyof CargoFormData, boolean>>;

// API Response types for specific operations
export interface TrackingNumberExistsResponse {
  exists: boolean;
  cargo?: CargoWithRelations;
}

export interface BulkOperationResponse {
  successful: number;
  failed: number;
  errors: string[];
}

// Release Authorization types
export type {
  ReleaseAuthorizationBase,
  ReleaseAuthorizationInsert,
  ReleaseAuthorizationUpdate,
  ReleaseAuthorizationWithRelations,
  ShipmentForRelease,
} from "./operations/release-authorizations";

// Document types
export type {
  DocumentUploadOptions,
  DocumentGenerationData,
  DocumentSearchOptions,
  DocumentStatistics,
} from "./operations/documents";

// Release Document types
export type {
  ReleaseDocumentGenerationOptions,
  BatchReleaseDocumentOptions,
  ReleaseDocumentResult,
  ReleaseDocumentStatistics,
} from "./operations/release-documents";
