// Mock data for testing layout components

export interface MockProduct {
  id: string;
  name: string;
  description: string;
  category: string;
  status: "ACTIVE" | "INACTIVE" | "PENDING" | "DISCONTINUED";
  price: number;
  stock: number;
  sku: string;
  brand: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  image_url?: string;
  rating: number;
  reviews_count: number;
}

export interface MockOrder {
  id: string;
  order_number: string;
  customer_name: string;
  customer_email: string;
  status: "PENDING" | "PROCESSING" | "SHIPPED" | "DELIVERED" | "CANCELLED";
  total_amount: number;
  items_count: number;
  shipping_address: string;
  payment_method: string;
  created_at: string;
  estimated_delivery: string;
}

export interface MockCustomer {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: "ACTIVE" | "INACTIVE" | "SUSPENDED";
  total_orders: number;
  total_spent: number;
  last_order_date: string;
  created_at: string;
  location: string;
  tier: "BRONZE" | "SILVER" | "GOLD" | "PLATINUM";
}

// Mock Products Data
export const mockProducts: MockProduct[] = [
  {
    id: "1",
    name: "Wireless Bluetooth Headphones",
    description: "High-quality wireless headphones with noise cancellation",
    category: "electronics",
    status: "ACTIVE",
    price: 199.99,
    stock: 45,
    sku: "WBH-001",
    brand: "TechSound",
    tags: ["wireless", "bluetooth", "noise-cancelling"],
    created_at: "2024-01-15T10:30:00Z",
    updated_at: "2024-01-20T14:22:00Z",
    rating: 4.5,
    reviews_count: 128,
  },
  {
    id: "2",
    name: "Organic Cotton T-Shirt",
    description: "Comfortable organic cotton t-shirt in various colors",
    category: "clothing",
    status: "ACTIVE",
    price: 29.99,
    stock: 120,
    sku: "OCT-002",
    brand: "EcoWear",
    tags: ["organic", "cotton", "sustainable"],
    created_at: "2024-01-16T09:15:00Z",
    updated_at: "2024-01-18T11:45:00Z",
    rating: 4.2,
    reviews_count: 89,
  },
  {
    id: "3",
    name: "Smart Fitness Watch",
    description: "Advanced fitness tracking with heart rate monitor",
    category: "electronics",
    status: "ACTIVE",
    price: 299.99,
    stock: 32,
    sku: "SFW-003",
    brand: "FitTech",
    tags: ["fitness", "smartwatch", "health"],
    created_at: "2024-01-17T16:20:00Z",
    updated_at: "2024-01-19T13:10:00Z",
    rating: 4.7,
    reviews_count: 203,
  },
  {
    id: "4",
    name: "Leather Laptop Bag",
    description: "Premium leather laptop bag with multiple compartments",
    category: "accessories",
    status: "PENDING",
    price: 149.99,
    stock: 18,
    sku: "LLB-004",
    brand: "LeatherCraft",
    tags: ["leather", "laptop", "professional"],
    created_at: "2024-01-18T12:00:00Z",
    updated_at: "2024-01-20T09:30:00Z",
    rating: 4.3,
    reviews_count: 67,
  },
  {
    id: "5",
    name: "Ceramic Coffee Mug Set",
    description: "Set of 4 handcrafted ceramic coffee mugs",
    category: "home",
    status: "ACTIVE",
    price: 39.99,
    stock: 75,
    sku: "CCM-005",
    brand: "HomeComfort",
    tags: ["ceramic", "coffee", "handcrafted"],
    created_at: "2024-01-19T08:45:00Z",
    updated_at: "2024-01-20T15:20:00Z",
    rating: 4.1,
    reviews_count: 45,
  },
];

// Mock Orders Data
export const mockOrders: MockOrder[] = [
  {
    id: "ORD-001",
    order_number: "ORD-2024-001",
    customer_name: "John Smith",
    customer_email: "<EMAIL>",
    status: "DELIVERED",
    total_amount: 199.99,
    items_count: 1,
    shipping_address: "123 Main St, New York, NY 10001",
    payment_method: "Credit Card",
    created_at: "2024-01-15T14:30:00Z",
    estimated_delivery: "2024-01-20T00:00:00Z",
  },
  {
    id: "ORD-002",
    order_number: "ORD-2024-002",
    customer_name: "Sarah Johnson",
    customer_email: "<EMAIL>",
    status: "SHIPPED",
    total_amount: 89.97,
    items_count: 3,
    shipping_address: "456 Oak Ave, Los Angeles, CA 90210",
    payment_method: "PayPal",
    created_at: "2024-01-18T10:15:00Z",
    estimated_delivery: "2024-01-23T00:00:00Z",
  },
  {
    id: "ORD-003",
    order_number: "ORD-2024-003",
    customer_name: "Mike Davis",
    customer_email: "<EMAIL>",
    status: "PROCESSING",
    total_amount: 449.98,
    items_count: 2,
    shipping_address: "789 Pine St, Chicago, IL 60601",
    payment_method: "Credit Card",
    created_at: "2024-01-20T16:45:00Z",
    estimated_delivery: "2024-01-25T00:00:00Z",
  },
];

// Mock Customers Data
export const mockCustomers: MockCustomer[] = [
  {
    id: "CUST-001",
    name: "John Smith",
    email: "<EMAIL>",
    phone: "******-0123",
    status: "ACTIVE",
    total_orders: 12,
    total_spent: 2399.88,
    last_order_date: "2024-01-15T14:30:00Z",
    created_at: "2023-06-15T09:00:00Z",
    location: "New York, NY",
    tier: "GOLD",
  },
  {
    id: "CUST-002",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "******-0456",
    status: "ACTIVE",
    total_orders: 8,
    total_spent: 1299.92,
    last_order_date: "2024-01-18T10:15:00Z",
    created_at: "2023-08-22T14:30:00Z",
    location: "Los Angeles, CA",
    tier: "SILVER",
  },
  {
    id: "CUST-003",
    name: "Mike Davis",
    email: "<EMAIL>",
    phone: "******-0789",
    status: "ACTIVE",
    total_orders: 5,
    total_spent: 899.95,
    last_order_date: "2024-01-20T16:45:00Z",
    created_at: "2023-11-10T11:20:00Z",
    location: "Chicago, IL",
    tier: "BRONZE",
  },
];

// Categories for filtering
export const productCategories = [
  { key: "electronics", label: "Electronics", count: 2 },
  { key: "clothing", label: "Clothing", count: 1 },
  { key: "accessories", label: "Accessories", count: 1 },
  { key: "home", label: "Home & Garden", count: 1 },
];

export const orderStatuses = [
  { key: "pending", label: "Pending", count: 0 },
  { key: "processing", label: "Processing", count: 1 },
  { key: "shipped", label: "Shipped", count: 1 },
  { key: "delivered", label: "Delivered", count: 1 },
  { key: "cancelled", label: "Cancelled", count: 0 },
];

export const customerTiers = [
  { key: "bronze", label: "Bronze", count: 1 },
  { key: "silver", label: "Silver", count: 1 },
  { key: "gold", label: "Gold", count: 1 },
  { key: "platinum", label: "Platinum", count: 0 },
];

// Statistics data
export const mockStats = {
  totalProducts: 5,
  activeProducts: 4,
  totalOrders: 3,
  totalRevenue: 739.94,
  totalCustomers: 3,
  averageOrderValue: 246.65,
  conversionRate: 3.2,
  customerSatisfaction: 94.5,
};

// Popular tags for filtering
export const popularTags = [
  "wireless",
  "organic",
  "fitness",
  "leather",
  "ceramic",
  "bluetooth",
  "sustainable",
  "handcrafted",
];
