"use client";

import { useState, Dispatch, SetStateAction } from "react";

import { Overview } from "@/modules/layouts/overview";
import { Listing } from "@/modules/listing";
import { type ColumnFilter } from "@/components/ui/filter-panel";

import { Badge } from "@workspace/ui/components/badge";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@workspace/ui/components/tabs";
import {
  Plus,
  Package,
  Users,
  ShoppingCart,
  DollarSign,
  Star,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  Download,
} from "lucide-react";
import {
  mockProducts,
  mockOrders,
  mockCustomers,
  mockStats,
  productCategories,
  orderStatuses,
  customerTiers,
  popularTags,
  type MockProduct,
  type MockOrder,
  type MockCustomer,
} from "./data";

// Helper functions for category styling
function getProductCategoryColor(category: string): string {
  const colors: Record<string, string> = {
    electronics: "#3b82f6",
    clothing: "#10b981",
    accessories: "#f59e0b",
    home: "#8b5cf6",
  };
  return colors[category] || "#6b7280";
}

function getProductCategoryIcon(category: string) {
  const icons: Record<string, any> = {
    electronics: Package,
    clothing: Users,
    accessories: Star,
    home: ShoppingCart,
  };
  return icons[category] || Package;
}

function getOrderStatusColor(status: string): string {
  const colors: Record<string, string> = {
    pending: "#f59e0b",
    processing: "#3b82f6",
    shipped: "#8b5cf6",
    delivered: "#10b981",
    cancelled: "#ef4444",
  };
  return colors[status] || "#6b7280";
}

function getOrderStatusIcon(status: string) {
  const icons: Record<string, any> = {
    pending: Package,
    processing: RefreshCw,
    shipped: ShoppingCart,
    delivered: Star,
    cancelled: Eye,
  };
  return icons[status] || ShoppingCart;
}

function getCustomerTierColor(tier: string): string {
  const colors: Record<string, string> = {
    bronze: "#cd7c2f",
    silver: "#9ca3af",
    gold: "#f59e0b",
    platinum: "#8b5cf6",
  };
  return colors[tier] || "#6b7280";
}

function getCustomerTierIcon(tier: string) {
  const icons: Record<string, any> = {
    bronze: Star,
    silver: Star,
    gold: Star,
    platinum: Star,
  };
  return icons[tier] || Users;
}

export function LayoutExample() {
  // State management
  const [viewMode, setViewMode] = useState<"cards" | "table">("cards");
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [columnFilters, setColumnFilters] = useState<ColumnFilter[]>([]);

  const itemsPerPage = 10;

  // Handle refresh
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  };

  return (
    <Overview className="p-6">
      <Overview.Header
        title="Business Dashboard"
        caption="Monitor your business performance and manage operations"
        actions={
          <>
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Item
            </Button>
          </>
        }
      />

      <Overview.Content>
        {/* Stat Cards */}
        <Listing.Statistics columns="grid-cols-4">
          <Listing.StatCard
            icon={Package}
            name="Total Products"
            value={mockStats.totalProducts}
            valueType="number"
            caption={
              <span className="text-xs text-green-600 flex items-center gap-1">
                <RefreshCw className="h-3 w-3" /> Real-time data
              </span>
            }
            color="primary"
            loading={loading}
          />
          <Listing.StatCard
            icon={ShoppingCart}
            name="Total Orders"
            value={mockStats.totalOrders}
            valueType="number"
            caption={
              <span className="text-xs text-blue-600 flex items-center gap-1">
                <RefreshCw className="h-3 w-3" /> +8% from last week
              </span>
            }
            color="blue"
            loading={loading}
          />
          <Listing.StatCard
            icon={DollarSign}
            name="Total Revenue"
            value={mockStats.totalRevenue}
            valueType="dollar"
            caption={
              <span className="text-xs text-green-600 flex items-center gap-1">
                <RefreshCw className="h-3 w-3" /> +15% from last month
              </span>
            }
            color="green"
            loading={loading}
          />
          <Listing.StatCard
            icon={Users}
            name="Total Customers"
            value={mockStats.totalCustomers}
            valueType="number"
            caption={
              <span className="text-xs text-amber-600 flex items-center gap-1">
                <RefreshCw className="h-3 w-3" /> +5% from last month
              </span>
            }
            color="amber"
            loading={loading}
          />
        </Listing.Statistics>

        {/* Tabs for Content Switching */}
        <Tabs defaultValue="products" className="w-full">
          <TabsList className="grid w-max grid-cols-3">
            <TabsTrigger value="products" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              Products
            </TabsTrigger>
            <TabsTrigger value="orders" className="flex items-center gap-2">
              <ShoppingCart className="h-4 w-4" />
              Orders
            </TabsTrigger>
            <TabsTrigger value="customers" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Customers
            </TabsTrigger>
          </TabsList>

          {/* Products Tab Content */}
          <TabsContent value="products" className="mt-6">
            <ProductsList
              viewMode={viewMode}
              setViewMode={setViewMode}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              categoryFilter={categoryFilter}
              setCategoryFilter={setCategoryFilter}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              itemsPerPage={itemsPerPage}
              loading={loading}
              onRefresh={handleRefresh}
              columnFilters={columnFilters}
              setColumnFilters={setColumnFilters}
            />
          </TabsContent>

          {/* Orders Tab Content */}
          <TabsContent value="orders" className="mt-6">
            <OrdersList
              viewMode={viewMode}
              setViewMode={setViewMode}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              categoryFilter={categoryFilter}
              setCategoryFilter={setCategoryFilter}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              itemsPerPage={itemsPerPage}
              loading={loading}
              onRefresh={handleRefresh}
              columnFilters={columnFilters}
              setColumnFilters={setColumnFilters}
            />
          </TabsContent>

          {/* Customers Tab Content */}
          <TabsContent value="customers" className="mt-6">
            <CustomersList
              viewMode={viewMode}
              setViewMode={setViewMode}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              categoryFilter={categoryFilter}
              setCategoryFilter={setCategoryFilter}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              itemsPerPage={itemsPerPage}
              loading={loading}
              onRefresh={handleRefresh}
              columnFilters={columnFilters}
              setColumnFilters={setColumnFilters}
            />
          </TabsContent>
        </Tabs>
      </Overview.Content>
    </Overview>
  );
}

// List Components for Tab Content

// Props interface for list components
interface ListProps {
  viewMode: "cards" | "table";
  setViewMode: (mode: "cards" | "table") => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  categoryFilter: string;
  setCategoryFilter: (category: string) => void;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  itemsPerPage: number;
  loading: boolean;
  onRefresh: () => void;
  columnFilters: ColumnFilter[];
  setColumnFilters: Dispatch<SetStateAction<ColumnFilter[]>>;
}

// Products List Component
function ProductsList({
  viewMode,
  setViewMode,
  searchTerm,
  setSearchTerm,
  categoryFilter,
  setCategoryFilter,
  currentPage,
  setCurrentPage,
  itemsPerPage,
  loading,
  onRefresh,
  columnFilters,
  setColumnFilters,
}: ListProps) {
  // Filter data based on search, category, and column filters
  const filteredProducts = mockProducts.filter((product) => {
    const matchesSearch =
      !searchTerm ||
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.brand.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory =
      categoryFilter === "all" || product.category === categoryFilter;

    // Apply column filters
    const matchesColumnFilters = columnFilters.every((filter) => {
      const value = (product as any)[filter.column];
      if (value === null || value === undefined) return false;
      const valueStr = value.toString().toLowerCase();
      const filterStr = filter.value.toLowerCase();
      return valueStr.includes(filterStr);
    });

    return matchesSearch && matchesCategory && matchesColumnFilters;
  });

  const totalItems = filteredProducts.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Define table columns for FilterPanel
  const tableColumns = [
    {
      key: "name",
      label: "Product Name",
      type: "string" as const,
      searchable: true,
    },
    { key: "brand", label: "Brand", type: "string" as const, searchable: true },
    { key: "price", label: "Price", type: "number" as const, searchable: true },
    { key: "stock", label: "Stock", type: "number" as const, searchable: true },
    { key: "sku", label: "SKU", type: "string" as const, searchable: true },
  ];

  // Handle column filter changes
  const handleColumnFilterAdd = (filter: ColumnFilter) => {
    setColumnFilters((prev) => [...prev, filter]);
  };

  const handleColumnFilterRemove = (index: number) => {
    setColumnFilters((prev) => prev.filter((_, i) => i !== index));
  };

  // Table columns for products
  const productColumns = [
    {
      key: "name",
      label: "Product Name",
      render: (product: MockProduct) => (
        <div>
          <div className="font-medium text-gray-900">{product.name}</div>
          <div className="text-sm text-gray-500">{product.sku}</div>
        </div>
      ),
    },
    {
      key: "category",
      label: "Category",
      render: (product: MockProduct) => (
        <Badge variant="secondary" className="capitalize">
          {product.category}
        </Badge>
      ),
    },
    {
      key: "price",
      label: "Price",
      render: (product: MockProduct) => (
        <span className="font-mono text-sm">${product.price}</span>
      ),
    },
    {
      key: "stock",
      label: "Stock",
      render: (product: MockProduct) => (
        <span
          className={`font-medium ${product.stock < 20 ? "text-red-600" : "text-green-600"}`}
        >
          {product.stock}
        </span>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (product: MockProduct) => (
        <Badge variant={product.status === "ACTIVE" ? "default" : "secondary"}>
          {product.status}
        </Badge>
      ),
    },
    {
      key: "rating",
      label: "Rating",
      render: (product: MockProduct) => (
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="text-sm">{product.rating}</span>
          <span className="text-xs text-gray-500">
            ({product.reviews_count})
          </span>
        </div>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      className: "text-right",
      render: () => (
        <div className="flex justify-end gap-1">
          <Button variant="ghost" size="sm">
            <Eye className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  const emptyState = (
    <div className="text-center py-12">
      <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
        <Package className="h-6 w-6 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-1">
        No products found
      </h3>
      <p className="text-gray-500 mb-4">
        {searchTerm
          ? "Try adjusting your search terms"
          : "Create your first product to get started"}
      </p>
      <Button className="gap-2">
        <Plus className="h-4 w-4" />
        Add Product
      </Button>
    </div>
  );

  return (
    <Listing className="space-y-6">
      <Listing.Filters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        onRefresh={onRefresh}
        loading={loading}
        popularTags={popularTags}
        columnFilters={columnFilters}
        onColumnFilterAdd={handleColumnFilterAdd}
        onColumnFilterRemove={handleColumnFilterRemove}
        enableDynamicFilters={true}
        columns={tableColumns}
        tableData={filteredProducts}
        defaultFilterColumn="name"
        autoSelectDefaultColumn={true}
      />

      <Listing.Controls
        entity="product"
        length={filteredProducts.length}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        categoryFilter={categoryFilter}
        onCategoryFilterChange={setCategoryFilter}
        categories={productCategories}
        actions={
          <div>
            <Button variant="outline" size="sm" className="mr-2">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              New Product
            </Button>
          </div>
        }
      />

      {viewMode === "cards" ? (
        <Listing.Cards
          data={filteredProducts}
          loading={loading}
          emptyState={emptyState}
          columns="grid-cols-3"
          groupByCategory={true}
          categories={productCategories.map((cat) => ({
            key: cat.key,
            name: cat.label,
            description: `${cat.count} products`,
            color: getProductCategoryColor(cat.key),
            icon: getProductCategoryIcon(cat.key),
          }))}
          getCategoryKey={(product) => product.category}
          renderCard={(product: MockProduct, category) => (
            <div className="p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div
                    className="p-1.5 rounded-md"
                    style={{
                      backgroundColor: category?.color
                        ? `${category.color}20`
                        : "#f3f4f6",
                      color: category?.color || "#6b7280",
                    }}
                  >
                    {category?.icon ? (
                      <category.icon className="h-3 w-3" />
                    ) : (
                      <Package className="h-3 w-3" />
                    )}
                  </div>
                  <h5 className="font-medium text-gray-900 truncate text-wrap">
                    {product.name.length > 35
                      ? `${product.name.substring(0, 35)}...`
                      : product.name}
                  </h5>
                </div>
                <Badge
                  variant={
                    product.status === "ACTIVE" ? "default" : "secondary"
                  }
                >
                  {product.status}
                </Badge>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Price</span>
                  <span className="text-gray-900 font-mono">
                    ${product.price}
                  </span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Stock</span>
                  <span
                    className={`font-medium ${product.stock < 20 ? "text-red-600" : "text-green-600"}`}
                  >
                    {product.stock}
                  </span>
                </div>

                <div className="flex flex-row justify-between gap-1">
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm">{product.rating}</span>
                    <span className="text-xs text-gray-500">
                      ({product.reviews_count})
                    </span>
                  </div>
                  <small className="text-gray-500 text-xs bg-gray-100 px-2 py-0.5 rounded-md">
                    {product.sku}
                  </small>
                </div>
              </div>
            </div>
          )}
          onItemClick={(product) => console.log("Product clicked:", product)}
        />
      ) : (
        <Listing.Table
          data={filteredProducts}
          columns={productColumns}
          loading={loading}
          emptyState={emptyState}
          pagination={{
            currentPage,
            totalPages,
            totalItems,
            itemsPerPage,
            onPageChange: setCurrentPage,
          }}
        />
      )}
    </Listing>
  );
}

// Orders List Component
function OrdersList({
  viewMode,
  setViewMode,
  searchTerm,
  setSearchTerm,
  categoryFilter,
  setCategoryFilter,
  currentPage,
  setCurrentPage,
  itemsPerPage,
  loading,
  onRefresh,
  columnFilters,
  setColumnFilters,
}: ListProps) {
  // Filter data based on search, category, and column filters
  const filteredOrders = mockOrders.filter((order) => {
    const matchesSearch =
      !searchTerm ||
      order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer_name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      categoryFilter === "all" || order.status.toLowerCase() === categoryFilter;

    // Apply column filters
    const matchesColumnFilters = columnFilters.every((filter) => {
      const value = (order as any)[filter.column];
      if (value === null || value === undefined) return false;
      const valueStr = value.toString().toLowerCase();
      const filterStr = filter.value.toLowerCase();
      return valueStr.includes(filterStr);
    });

    return matchesSearch && matchesStatus && matchesColumnFilters;
  });

  const totalItems = filteredOrders.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Define table columns for FilterPanel
  const tableColumns = [
    {
      key: "order_number",
      label: "Order Number",
      type: "string" as const,
      searchable: true,
    },
    {
      key: "customer_name",
      label: "Customer Name",
      type: "string" as const,
      searchable: true,
    },
    {
      key: "customer_email",
      label: "Customer Email",
      type: "string" as const,
      searchable: true,
    },
    {
      key: "total_amount",
      label: "Total Amount",
      type: "number" as const,
      searchable: true,
    },
    {
      key: "items_count",
      label: "Items Count",
      type: "number" as const,
      searchable: true,
    },
  ];

  // Handle column filter changes
  const handleColumnFilterAdd = (filter: ColumnFilter) => {
    setColumnFilters((prev) => [...prev, filter]);
  };

  const handleColumnFilterRemove = (index: number) => {
    setColumnFilters((prev) => prev.filter((_, i) => i !== index));
  };

  const orderColumns = [
    {
      key: "order_number",
      label: "Order Number",
      render: (order: MockOrder) => (
        <div className="font-mono text-sm text-primary">
          {order.order_number}
        </div>
      ),
    },
    {
      key: "customer",
      label: "Customer",
      render: (order: MockOrder) => (
        <div>
          <div className="font-medium">{order.customer_name}</div>
          <div className="text-sm text-gray-500">{order.customer_email}</div>
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (order: MockOrder) => (
        <Badge variant={order.status === "DELIVERED" ? "default" : "secondary"}>
          {order.status}
        </Badge>
      ),
    },
    {
      key: "total",
      label: "Total",
      render: (order: MockOrder) => (
        <span className="font-mono">${order.total_amount}</span>
      ),
    },
    {
      key: "items",
      label: "Items",
      render: (order: MockOrder) => <span>{order.items_count} items</span>,
    },
  ];

  return (
    <Listing className="space-y-6">
      <Listing.Filters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        onRefresh={onRefresh}
        loading={loading}
        columnFilters={columnFilters}
        onColumnFilterAdd={handleColumnFilterAdd}
        onColumnFilterRemove={handleColumnFilterRemove}
        enableDynamicFilters={true}
        columns={tableColumns}
        tableData={filteredOrders}
        defaultFilterColumn="order_number"
        autoSelectDefaultColumn={true}
      />

      <Listing.Controls
        entity="orders"
        length={filteredOrders.length}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        categoryFilter={categoryFilter}
        onCategoryFilterChange={setCategoryFilter}
        categories={orderStatuses}
      />

      {viewMode === "cards" ? (
        <Listing.Cards
          data={filteredOrders}
          loading={loading}
          columns="grid-cols-2"
          groupByCategory={true}
          categories={orderStatuses.map((status) => ({
            key: status.key,
            name: status.label,
            description: `${status.count} orders`,
            color: getOrderStatusColor(status.key),
            icon: getOrderStatusIcon(status.key),
          }))}
          getCategoryKey={(order) => order.status.toLowerCase()}
          renderCard={(order: MockOrder, category) => (
            <div className="p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div
                    className="p-1.5 rounded-md"
                    style={{
                      backgroundColor: category?.color
                        ? `${category.color}20`
                        : "#f3f4f6",
                      color: category?.color || "#6b7280",
                    }}
                  >
                    {category?.icon ? (
                      <category.icon className="h-3 w-3" />
                    ) : (
                      <ShoppingCart className="h-3 w-3" />
                    )}
                  </div>
                  <h5 className="font-medium text-gray-900 font-mono text-sm">
                    {order.order_number}
                  </h5>
                </div>
                <Badge
                  variant={
                    order.status === "DELIVERED" ? "default" : "secondary"
                  }
                >
                  {order.status}
                </Badge>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Customer</span>
                  <span className="text-gray-900 font-medium">
                    {order.customer_name}
                  </span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Total</span>
                  <span className="text-gray-900 font-mono font-bold">
                    ${order.total_amount}
                  </span>
                </div>

                <div className="flex flex-row justify-between gap-1">
                  <span className="text-xs text-gray-500">
                    {order.customer_email}
                  </span>
                  <small className="text-gray-500 text-xs bg-gray-100 px-2 py-0.5 rounded-md">
                    {order.items_count} items
                  </small>
                </div>
              </div>
            </div>
          )}
          onItemClick={(order) => console.log("Order clicked:", order)}
        />
      ) : (
        <Listing.Table
          data={filteredOrders}
          columns={orderColumns}
          loading={loading}
          pagination={{
            currentPage,
            totalPages,
            totalItems,
            itemsPerPage,
            onPageChange: setCurrentPage,
          }}
        />
      )}
    </Listing>
  );
}

// Customers List Component
function CustomersList({
  viewMode,
  setViewMode,
  searchTerm,
  setSearchTerm,
  categoryFilter,
  setCategoryFilter,
  currentPage,
  setCurrentPage,
  itemsPerPage,
  loading,
  onRefresh,
  columnFilters,
  setColumnFilters,
}: ListProps) {
  // Filter data based on search, category, and column filters
  const filteredCustomers = mockCustomers.filter((customer) => {
    const matchesSearch =
      !searchTerm ||
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesTier =
      categoryFilter === "all" ||
      customer.tier.toLowerCase() === categoryFilter;

    // Apply column filters
    const matchesColumnFilters = columnFilters.every((filter) => {
      const value = (customer as any)[filter.column];
      if (value === null || value === undefined) return false;
      const valueStr = value.toString().toLowerCase();
      const filterStr = filter.value.toLowerCase();
      return valueStr.includes(filterStr);
    });

    return matchesSearch && matchesTier && matchesColumnFilters;
  });

  const totalItems = filteredCustomers.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Define table columns for FilterPanel
  const tableColumns = [
    {
      key: "name",
      label: "Customer Name",
      type: "string" as const,
      searchable: true,
    },
    { key: "email", label: "Email", type: "string" as const, searchable: true },
    { key: "tier", label: "Tier", type: "enum" as const, searchable: true },
    {
      key: "total_orders",
      label: "Total Orders",
      type: "number" as const,
      searchable: true,
    },
    {
      key: "total_spent",
      label: "Total Spent",
      type: "number" as const,
      searchable: true,
    },
    {
      key: "location",
      label: "Location",
      type: "string" as const,
      searchable: true,
    },
  ];

  // Handle column filter changes
  const handleColumnFilterAdd = (filter: ColumnFilter) => {
    setColumnFilters((prev) => [...prev, filter]);
  };

  const handleColumnFilterRemove = (index: number) => {
    setColumnFilters((prev) => prev.filter((_, i) => i !== index));
  };

  const customerColumns = [
    {
      key: "name",
      label: "Customer",
      render: (customer: MockCustomer) => (
        <div>
          <div className="font-medium">{customer.name}</div>
          <div className="text-sm text-gray-500">{customer.email}</div>
        </div>
      ),
    },
    {
      key: "tier",
      label: "Tier",
      render: (customer: MockCustomer) => (
        <Badge variant="secondary" className="capitalize">
          {customer.tier}
        </Badge>
      ),
    },
    {
      key: "orders",
      label: "Orders",
      render: (customer: MockCustomer) => <span>{customer.total_orders}</span>,
    },
    {
      key: "spent",
      label: "Total Spent",
      render: (customer: MockCustomer) => (
        <span className="font-mono">${customer.total_spent}</span>
      ),
    },
    {
      key: "location",
      label: "Location",
      render: (customer: MockCustomer) => (
        <span className="text-sm">{customer.location}</span>
      ),
    },
  ];

  return (
    <Listing className="space-y-6">
      <Listing.Filters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        onRefresh={onRefresh}
        loading={loading}
        columnFilters={columnFilters}
        onColumnFilterAdd={handleColumnFilterAdd}
        onColumnFilterRemove={handleColumnFilterRemove}
        enableDynamicFilters={true}
        columns={tableColumns}
        tableData={filteredCustomers}
        defaultFilterColumn="name"
        autoSelectDefaultColumn={true}
      />

      <Listing.Controls
        entity="customers"
        length={filteredCustomers.length}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        categoryFilter={categoryFilter}
        onCategoryFilterChange={setCategoryFilter}
        categories={customerTiers}
      />

      {viewMode === "cards" ? (
        <Listing.Cards
          data={filteredCustomers}
          loading={loading}
          columns="grid-cols-3"
          groupByCategory={true}
          categories={customerTiers.map((tier) => ({
            key: tier.key,
            name: tier.label,
            description: `${tier.count} customers`,
            color: getCustomerTierColor(tier.key),
            icon: getCustomerTierIcon(tier.key),
          }))}
          getCategoryKey={(customer) => customer.tier.toLowerCase()}
          renderCard={(customer: MockCustomer, category) => (
            <div className="p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div
                    className="p-1.5 rounded-md"
                    style={{
                      backgroundColor: category?.color
                        ? `${category.color}20`
                        : "#f3f4f6",
                      color: category?.color || "#6b7280",
                    }}
                  >
                    {category?.icon ? (
                      <category.icon className="h-3 w-3" />
                    ) : (
                      <Users className="h-3 w-3" />
                    )}
                  </div>
                  <h5 className="font-medium text-gray-900">{customer.name}</h5>
                </div>
                <Badge variant="secondary" className="capitalize">
                  {customer.tier}
                </Badge>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Total Spent</span>
                  <span className="text-gray-900 font-mono font-bold">
                    ${customer.total_spent}
                  </span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Orders</span>
                  <span className="text-gray-900 font-medium">
                    {customer.total_orders}
                  </span>
                </div>

                <div className="flex flex-row justify-between gap-1">
                  <span className="text-xs text-gray-500">
                    {customer.email}
                  </span>
                  <small className="text-gray-500 text-xs bg-gray-100 px-2 py-0.5 rounded-md">
                    {customer.location}
                  </small>
                </div>
              </div>
            </div>
          )}
          onItemClick={(customer) => console.log("Customer clicked:", customer)}
        />
      ) : (
        <Listing.Table
          data={filteredCustomers}
          columns={customerColumns}
          loading={loading}
          pagination={{
            currentPage,
            totalPages,
            totalItems,
            itemsPerPage,
            onPageChange: setCurrentPage,
          }}
        />
      )}
    </Listing>
  );
}
