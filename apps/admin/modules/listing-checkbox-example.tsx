"use client";

import React, { useState } from "react";
import { Listing } from "@/modules/listing";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Trash2, Download, Mail } from "lucide-react";

// Example data
const sampleData = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active",
    role: "admin",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "inactive",
    role: "user",
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active",
    role: "user",
  },
  {
    id: "4",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "pending",
    role: "moderator",
  },
  {
    id: "5",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active",
    role: "user",
  },
];

// Table columns configuration
const columns = [
  {
    key: "name",
    label: "Name",
    render: (item: any) => (
      <div className="font-medium text-gray-900">{item.name}</div>
    ),
  },
  {
    key: "email",
    label: "Email",
    render: (item: any) => <div className="text-gray-600">{item.email}</div>,
  },
  {
    key: "status",
    label: "Status",
    render: (item: any) => (
      <Badge
        variant={
          item.status === "active"
            ? "default"
            : item.status === "pending"
              ? "secondary"
              : "destructive"
        }
      >
        {item.status}
      </Badge>
    ),
  },
  {
    key: "role",
    label: "Role",
    render: (item: any) => (
      <span className="capitalize text-gray-700">{item.role}</span>
    ),
  },
];

export function ListingCheckboxExample() {
  const [selectedRowIds, setSelectedRowIds] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<"cards" | "table">("table");

  const handleSelectionChange = (newSelectedIds: string[]) => {
    setSelectedRowIds(newSelectedIds);
  };

  const handleBulkDelete = () => {
    if (selectedRowIds.length === 0) return;

    // Implement bulk delete logic here
    alert(
      `Would delete ${selectedRowIds.length} items: ${selectedRowIds.join(", ")}`
    );
  };

  const handleBulkExport = () => {
    if (selectedRowIds.length === 0) return;

    // Implement bulk export logic here
    alert(
      `Would export ${selectedRowIds.length} items: ${selectedRowIds.join(", ")}`
    );
  };

  const handleBulkEmail = () => {
    if (selectedRowIds.length === 0) return;

    const selectedItems = sampleData.filter((item) =>
      selectedRowIds.includes(item.id)
    );
    const emails = selectedItems.map((item) => item.email);
    console.log("Sending email to:", emails);
    alert(`Would send email to: ${emails.join(", ")}`);
  };

  return (
    <div className="p-6 space-y-6">
      <Listing>
        <Listing.Header
          title="Users with Checkbox Selection"
          caption="Example of table with checkbox functionality"
          actions={
            <div className="flex gap-2">
              {selectedRowIds.length > 0 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkEmail}
                    className="gap-2"
                  >
                    <Mail size={16} />
                    Email ({selectedRowIds.length})
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkExport}
                    className="gap-2"
                  >
                    <Download size={16} />
                    Export ({selectedRowIds.length})
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBulkDelete}
                    className="gap-2"
                  >
                    <Trash2 size={16} />
                    Delete ({selectedRowIds.length})
                  </Button>
                </>
              )}
              <Button>Add User</Button>
            </div>
          }
        />

        <Listing.Controls
          entity="users"
          length={sampleData.length}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
        />

        {viewMode === "table" && (
          <Listing.Table
            data={sampleData}
            columns={columns}
            enableCheckboxes={true}
            selectedRowIds={selectedRowIds}
            onSelectionChange={handleSelectionChange}
            getRowId={(item) => item.id}
            onRowClick={(item) => {
              console.log("Row clicked:", item);
              // Handle row click (e.g., navigate to detail page)
            }}
          />
        )}

        {viewMode === "cards" && (
          <div className="text-center py-12 text-gray-500">
            Card view doesn't support checkboxes yet. Switch to table view to
            see checkbox functionality.
          </div>
        )}
      </Listing>

      {/* Debug info */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-900 mb-2">Debug Info:</h3>
        <p className="text-sm text-gray-600">
          Selected Row IDs:{" "}
          {selectedRowIds.length > 0 ? selectedRowIds.join(", ") : "None"}
        </p>
        <p className="text-sm text-gray-600">
          Total Items: {sampleData.length}
        </p>
        <p className="text-sm text-gray-600">
          Selection Count: {selectedRowIds.length}
        </p>
      </div>
    </div>
  );
}
